<?php

namespace Tests\Feature;

use App\Http\Controllers\ScheduleMatchController;
use App\Models\Location;
use App\Models\Stage;
use App\Models\StageMatch;
use App\Models\Tournament;
use App\Utils\PerformanceMonitor;
use App\Utils\TimezoneHelper;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class ScheduleMatchOptimizationTest extends TestCase
{
    use RefreshDatabase;

    private ScheduleMatchController $controller;
    private Tournament $tournament;
    private Stage $stage;
    private array $locations;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->controller = new ScheduleMatchController();
        
        // Create test data
        $this->tournament = Tournament::factory()->create([
            'type' => config('constants.tournament_types.league')
        ]);
        
        $this->stage = Stage::factory()->create([
            'tournament_id' => $this->tournament->id,
            'type' => config('constants.tournament_types.league')
        ]);
        
        $this->locations = Location::factory()->count(3)->create()->toArray();
    }

    /**
     * Test performance improvements with large dataset
     */
    public function test_performance_with_large_dataset()
    {
        // Create a large number of matches to test performance
        $matchCount = 500;
        StageMatch::factory()->count($matchCount)->create([
            'stage_id' => $this->stage->id,
            'status' => null // Ensure they're not cancelled
        ]);

        $request = new Request([
            'tournament_id' => $this->tournament->id,
            'stage_id' => $this->stage->id,
            'list_location_ids' => array_column($this->locations, 'id'),
            'begin_date' => '2025-07-01',
            'begin_time' => '09:00',
            'end_time' => '18:00',
            'match_duration' => 90,
            'break_duration' => 15,
            'chunk_size' => 50
        ]);

        $request->headers->set('X-Time-Zone', 'UTC');

        // Monitor performance
        PerformanceMonitor::startTimer('large_dataset_test');
        
        $response = $this->controller->scheduleMatches($request);
        
        $metrics = PerformanceMonitor::endTimer('large_dataset_test', [
            'match_count' => $matchCount,
            'location_count' => count($this->locations)
        ]);

        // Assert response is successful
        $this->assertEquals(200, $response->getStatusCode());
        
        // Assert performance is within acceptable limits
        $this->assertLessThan(120, $metrics['execution_time'], 'Execution time should be under 2 minutes');
        $this->assertLessThan(512, $metrics['memory_used'], 'Memory usage should be under 512MB');
        
        // Assert reasonable query count (should be much less than match count due to batching)
        $this->assertLessThan($matchCount / 2, $metrics['queries_executed'], 'Query count should be optimized');
    }

    /**
     * Test chunking functionality
     */
    public function test_chunking_functionality()
    {
        $matchCount = 100;
        StageMatch::factory()->count($matchCount)->create([
            'stage_id' => $this->stage->id,
            'status' => null
        ]);

        $chunkSize = 25;
        $request = new Request([
            'tournament_id' => $this->tournament->id,
            'stage_id' => $this->stage->id,
            'list_location_ids' => array_column($this->locations, 'id'),
            'begin_date' => '2025-07-01',
            'begin_time' => '09:00',
            'end_time' => '18:00',
            'match_duration' => 90,
            'break_duration' => 15,
            'chunk_size' => $chunkSize
        ]);

        $request->headers->set('X-Time-Zone', 'UTC');

        $response = $this->controller->scheduleMatches($request);
        
        $this->assertEquals(200, $response->getStatusCode());
        
        // Verify all matches were scheduled
        $scheduledCount = DB::table('schedule_matches')
            ->where('tournament_id', $this->tournament->id)
            ->count();
            
        $this->assertEquals($matchCount, $scheduledCount);
    }

    /**
     * Test cancel match types filtering
     */
    public function test_cancel_match_types_filtering()
    {
        $cancelMatchTypes = config('constants.cancel_match_types');
        
        // Create matches with various statuses
        StageMatch::factory()->count(10)->create([
            'stage_id' => $this->stage->id,
            'status' => null // Valid matches
        ]);
        
        StageMatch::factory()->count(5)->create([
            'stage_id' => $this->stage->id,
            'status' => $cancelMatchTypes[0] // Cancelled matches
        ]);

        $request = new Request([
            'tournament_id' => $this->tournament->id,
            'stage_id' => $this->stage->id,
            'list_location_ids' => array_column($this->locations, 'id'),
            'begin_date' => '2025-07-01',
            'begin_time' => '09:00',
            'end_time' => '18:00',
            'match_duration' => 90,
            'break_duration' => 15
        ]);

        $request->headers->set('X-Time-Zone', 'UTC');

        $response = $this->controller->scheduleMatches($request);
        
        $this->assertEquals(200, $response->getStatusCode());
        
        // Only valid matches should be scheduled (10, not 15)
        $scheduledCount = DB::table('schedule_matches')
            ->where('tournament_id', $this->tournament->id)
            ->count();
            
        $this->assertEquals(10, $scheduledCount);
    }

    /**
     * Test timezone handling optimization
     */
    public function test_timezone_handling()
    {
        StageMatch::factory()->count(20)->create([
            'stage_id' => $this->stage->id,
            'status' => null
        ]);

        $request = new Request([
            'tournament_id' => $this->tournament->id,
            'stage_id' => $this->stage->id,
            'list_location_ids' => array_column($this->locations, 'id'),
            'begin_date' => '2025-07-01',
            'begin_time' => '09:00',
            'end_time' => '18:00',
            'match_duration' => 90,
            'break_duration' => 15
        ]);

        // Test with different timezones
        $timezones = ['UTC', 'America/New_York', 'Europe/London', 'Asia/Tokyo'];
        
        foreach ($timezones as $timezone) {
            $request->headers->set('X-Time-Zone', $timezone);
            
            $response = $this->controller->scheduleMatches($request);
            $this->assertEquals(200, $response->getStatusCode());
            
            // Verify timezone helper is working
            $this->assertTrue(TimezoneHelper::isValidTimezone($timezone));
        }
    }

    /**
     * Test batch operations efficiency
     */
    public function test_batch_operations_efficiency()
    {
        $matchCount = 50;
        StageMatch::factory()->count($matchCount)->create([
            'stage_id' => $this->stage->id,
            'status' => null
        ]);

        // Enable query logging to count queries
        DB::enableQueryLog();

        $request = new Request([
            'tournament_id' => $this->tournament->id,
            'stage_id' => $this->stage->id,
            'list_location_ids' => array_column($this->locations, 'id'),
            'begin_date' => '2025-07-01',
            'begin_time' => '09:00',
            'end_time' => '18:00',
            'match_duration' => 90,
            'break_duration' => 15,
            'chunk_size' => 25
        ]);

        $request->headers->set('X-Time-Zone', 'UTC');

        $response = $this->controller->scheduleMatches($request);
        
        $queries = DB::getQueryLog();
        DB::disableQueryLog();

        $this->assertEquals(200, $response->getStatusCode());
        
        // With batching, query count should be significantly less than match count
        $this->assertLessThan($matchCount, count($queries), 'Batch operations should reduce query count');
    }

    /**
     * Test async processing threshold
     */
    public function test_async_processing_threshold()
    {
        // Create enough matches to trigger async processing
        $matchCount = 100;
        StageMatch::factory()->count($matchCount)->create([
            'stage_id' => $this->stage->id,
            'status' => null
        ]);

        $request = new Request([
            'tournament_id' => $this->tournament->id,
            'stage_id' => $this->stage->id,
            'list_location_ids' => array_column($this->locations, 'id'),
            'begin_date' => '2025-07-01',
            'begin_time' => '09:00',
            'end_time' => '18:00',
            'match_duration' => 90,
            'break_duration' => 15
        ]);

        $request->headers->set('X-Time-Zone', 'UTC');

        $response = $this->controller->scheduleMatchesAsync($request);
        
        // Should return 202 for async processing
        $this->assertEquals(202, $response->getStatusCode());
        
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('job_id', $responseData);
        $this->assertArrayHasKey('estimated_matches', $responseData);
        $this->assertEquals('processing', $responseData['status']);
    }

    /**
     * Test memory usage optimization
     */
    public function test_memory_usage_optimization()
    {
        $matchCount = 200;
        StageMatch::factory()->count($matchCount)->create([
            'stage_id' => $this->stage->id,
            'status' => null
        ]);

        $initialMemory = memory_get_usage(true);

        $request = new Request([
            'tournament_id' => $this->tournament->id,
            'stage_id' => $this->stage->id,
            'list_location_ids' => array_column($this->locations, 'id'),
            'begin_date' => '2025-07-01',
            'begin_time' => '09:00',
            'end_time' => '18:00',
            'match_duration' => 90,
            'break_duration' => 15,
            'chunk_size' => 50
        ]);

        $request->headers->set('X-Time-Zone', 'UTC');

        $response = $this->controller->scheduleMatches($request);
        
        $peakMemory = memory_get_peak_usage(true);
        $memoryIncrease = ($peakMemory - $initialMemory) / 1024 / 1024; // MB

        $this->assertEquals(200, $response->getStatusCode());
        
        // Memory increase should be reasonable for the dataset size
        $this->assertLessThan(100, $memoryIncrease, 'Memory usage should be optimized');
    }

    protected function tearDown(): void
    {
        PerformanceMonitor::clear();
        TimezoneHelper::clearCache();
        parent::tearDown();
    }
}
