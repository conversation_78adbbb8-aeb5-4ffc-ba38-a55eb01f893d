<?php

namespace App\Console\Commands;

use App\Jobs\ProcessScheduleMatchesJob;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TestScheduleJobCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'schedule:test-job {tournament_id : Tournament ID to test}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test schedule job dispatch and processing';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $tournamentId = $this->argument('tournament_id');

        $this->info("Testing schedule job dispatch for tournament: {$tournamentId}");

        // Prepare test job data with all required fields
        $jobData = [
            'tournament_id' => $tournamentId,
            'list_location_ids' => [1], // Minimal test data
            'begin_date' => '2025-07-01',
            'begin_time' => '09:00',
            'end_time' => '18:00', // Add required end_time
            'match_duration' => 90,
            'break_duration' => 15,
            'stage_id' => -1,
            'nums_of_referees' => 0,
            'list_referee_ids' => [],
            'list_group_names' => [],
            'recreate_all_stage' => false,
            'timezone' => 'UTC'
        ];

        $jobId = 'test_' . uniqid();

        $this->info("Job ID: {$jobId}");

        // Check jobs before dispatch
        $jobsBefore = DB::table('jobs')->count();
        $this->line("Jobs in queue before dispatch: {$jobsBefore}");

        try {
            // Dispatch the job
            $this->info("Dispatching job...");
            ProcessScheduleMatchesJob::dispatch($jobData, $jobId);

            // Check jobs after dispatch
            $jobsAfter = DB::table('jobs')->count();
            $this->line("Jobs in queue after dispatch: {$jobsAfter}");

            if ($jobsAfter > $jobsBefore) {
                $this->info("✅ Job successfully dispatched!");

                // Find the specific job
                $job = DB::table('jobs')
                    ->where('payload', 'like', '%' . $jobId . '%')
                    ->first();

                if ($job) {
                    $this->info("✅ Job found in database:");
                    $this->line("  - Job ID: {$job->id}");
                    $this->line("  - Queue: {$job->queue}");
                    $this->line("  - Attempts: {$job->attempts}");
                    $this->line("  - Created: " . date('Y-m-d H:i:s', $job->created_at));

                    $this->info("\nTo process this job, run:");
                    $this->line("php artisan queue:work --queue={$job->queue} --once");
                } else {
                    $this->error("❌ Job was dispatched but not found in database");
                }
            } else {
                $this->error("❌ Job was not added to queue");
            }

        } catch (\Exception $e) {
            $this->error("❌ Failed to dispatch job: " . $e->getMessage());
            $this->line("Error details: " . $e->getTraceAsString());
        }

        // Show current queue status
        $this->showQueueStatus();
    }

    private function showQueueStatus()
    {
        $this->info("\n--- Current Queue Status ---");

        $queues = ['default', 'light-scheduling', 'medium-scheduling', 'heavy-scheduling'];

        foreach ($queues as $queue) {
            $count = DB::table('jobs')->where('queue', $queue)->count();
            $this->line("{$queue}: {$count} jobs");
        }

        $totalJobs = DB::table('jobs')->count();
        $failedJobs = DB::table('failed_jobs')->count();

        $this->line("Total jobs: {$totalJobs}");
        $this->line("Failed jobs: {$failedJobs}");
    }
}
