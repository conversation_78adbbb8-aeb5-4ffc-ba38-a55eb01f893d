<?php
namespace App\Http\Controllers;

use App\Models\ScheduleBreak;
use App\Models\ScheduleConfig;
use App\Models\ScheduleMatch;
use App\Models\ScheduleTimeSlot;
use App\Models\SeasonReferee;

use App\Models\Stage;
use App\Models\StageMatch;
use Carbon\Carbon;
use DateTime;
use DateTimeZone;
use Exception;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ScheduleTimeSlotController extends Controller
{

    public function getScheduleTimeSlots(Request $request, $tournament_id)
    {
        try {

            $request->validate([
                "date" => "required|date_format:Y-m-d",
            ]);

            $timezone = request()->header('X-Time-Zone') ?? 'UTC';

            // Get configs
            $tournamentScheduleConfigs = ScheduleConfig::where('tournament_id', $tournament_id)
                ->with('tournament', 'location')
                ->orderBy('begin_date', 'asc')
                ->orderBy('begin_time', 'asc')
                ->get()->filter(function ($config) use ($request, $timezone) {
                    $dateInLocal = $request->get('date');
                    $beginDateInLocal = Carbon::parse($config->begin_date->format('Y-m-d') . " " . $config->begin_time->format('H:i:s'), 'UTC')->setTimezone($timezone);

                    return $beginDateInLocal->format('Y-m-d') == $dateInLocal;
                });

            $scheduleTimeSlots = ScheduleTimeSlot::where('tournament_id', $tournament_id)
                ->with(['tournament', 'location', 'stage', 'scheduleMatch', 'break', 'scheduleMatch.match.homeTeam', 'scheduleMatch.match.awayTeam'])
                ->whereNotNull('location_id')
                ->whereNotNull('start_time')
                ->whereNotNull('end_time')
                ->orderBy('slot_index', 'asc')
                ->orderBy('type', 'desc')
                ->orderBy('stage_id', 'asc')
                ->orderBy('location_id', 'asc')
                ->get()->filter(function ($timeSlot) use ($request, $timezone) {
                    $dateInLocal = $request->get('date');
                    $startTimeInLocal = Carbon::parse($timeSlot->start_time, 'UTC')->setTimezone($timezone);

                    return $startTimeInLocal->format('Y-m-d') == $dateInLocal;
                });

            // Collect all unique referee IDs from schedule matches to fetch referee details
            $allRefereeIds = collect();
            foreach ($scheduleTimeSlots as $timeSlot) {
                if ($timeSlot->scheduleMatch && $timeSlot->scheduleMatch->referee_ids) {
                    $allRefereeIds = $allRefereeIds->merge($timeSlot->scheduleMatch->referee_ids);
                }
            }

            $allRefereeIds = $allRefereeIds->unique()->filter()->values();

            // Fetch all referee details
            $refereeDetails = [];
            if ($allRefereeIds->isNotEmpty()) {
                $referees = SeasonReferee::whereIn('id', $allRefereeIds)->with('user')->get();
                $refereeDetails = $referees->keyBy('id')->toArray();
            }

            $groupedMatches = [];

            $metadata = [
                'locations' => [],
                'configs' => [],
            ];

            foreach ($tournamentScheduleConfigs as $config) {
                $parsedDateTime = Carbon::parse($config->begin_date->format('Y-m-d') . " " . $config->begin_time->format('H:i:s'), 'UTC')->setTimezone($timezone);
                $metadata['configs'][] = array_merge($config->toArray(), [
                    "begin_date" => $parsedDateTime->format('Y-m-d'),
                    "begin_time" => $parsedDateTime->format('H:i:s'),
                ]); // parse begin date to local timezone

                $locationName = $config->location ? $config->location->name : 'Unknown Location';

                // init location name if it not exists in $metadata
                if ($locationName !== 'Unknown Location' && !isset($metadata['locations'][$locationName])) {
                    $metadata['locations'][$locationName] = array_merge($config->location->toArray(), [
                        "begin_time" => $config->begin_time,
                    ]);
                }

                // track expected dates from configs (for empty location handling later)
                $configDate = Carbon::parse($config->begin_date->format('Y-m-d') . " " . $config->begin_time->format('H:i:s'), 'UTC')->setTimezone($timezone)->format('Y-m-d');

                // init date if not exists in $groupedMatches
                if (!isset($groupedMatches[$configDate])) {
                    $groupedMatches[$configDate] = [];
                }
            }

            foreach ($scheduleTimeSlots as $timeSlot) {

                // skip if no location or no start time or no end time
                if (!$timeSlot->location || !$timeSlot->start_time || !$timeSlot->end_time) {
                    continue;
                }

                $locationName = $timeSlot->location->name;

                // parse start time to local timezone to determine the correct date group
                $localDate = Carbon::parse($timeSlot->start_time, 'UTC')->setTimezone($timezone)->format('Y-m-d');

                // init object if not exist - use the timezone-converted date
                if (!isset($groupedMatches[$localDate])) {
                    $groupedMatches[$localDate] = [];
                }
                if (!isset($groupedMatches[$localDate][$locationName])) {
                    $groupedMatches[$localDate][$locationName] = [];
                }

                $timeSlotData = [
                    'type' => $timeSlot->type,
                    'start_time' => $timeSlot->start_time,
                    'end_time' => $timeSlot->end_time,
                ];

                $scheduleData = ($timeSlot->scheduleMatch ?? $timeSlot->break)?->toArray() ?? [];

                if ($timeSlot->scheduleMatch && $timeSlot->scheduleMatch->referee_ids) {
                    $matchReferees = [];
                    foreach ($timeSlot->scheduleMatch->referee_ids as $refereeId) {
                        if (isset($refereeDetails[$refereeId])) {
                            $matchReferees[] = $refereeDetails[$refereeId];
                        }
                    }
                    $scheduleData['referees'] = $matchReferees;
                } else {
                    $scheduleData['referees'] = [];
                }

                $groupedMatches[$localDate][$locationName][] = array_merge($timeSlotData, $scheduleData);
            }

            // Handle empty locations - ensure locations with configs but no matches appear in grouped results
            // Check each config to see if its location-date combination has matches
            foreach ($tournamentScheduleConfigs as $config) {
                if (!$config->location) {
                    continue;
                }

                $locationName = $config->location->name;

                // Convert config date to timezone for comparison
                $configDate = Carbon::parse($config->begin_date->format('Y-m-d') . " " . $config->begin_time->format('H:i:s'), 'UTC')
                    ->setTimezone($timezone)
                    ->format('Y-m-d');

                // Ensure the date exists in groupedMatches
                if (!isset($groupedMatches[$configDate])) {
                    $groupedMatches[$configDate] = [];
                }

                // If this location doesn't have matches for this specific date, create empty array
                if (!isset($groupedMatches[$configDate][$locationName])) {
                    $groupedMatches[$configDate][$locationName] = [];
                }
            }

            $conflicts = (new ScheduleMatchController())->getScheduleConflict($tournament_id, $request->get('date'), $timezone);

            return response()->json([
                'data' => $groupedMatches,
                'metadata' => $metadata,
                'conflicts' => $conflicts,
            ]);
        } catch (Exception $error) {
            Log::error("Error fetching schedule time slots: " . $error);
            return [];
        }
    }

    public function getUnScheduledMatches($tournament_id)
    {
        try {
            // Get cancel match types from config/constants
            $cancelMatchTypes = config('constants.cancel_match_types', []);

            $unscheduledMatches = ScheduleTimeSlot::where('tournament_id', $tournament_id)
                ->with(['tournament', 'scheduleMatch.match', 'scheduleMatch.match.homeTeam', 'scheduleMatch.match.awayTeam'])
                ->whereNull('location_id')
                ->whereNull('start_time')
                ->whereNull('end_time')
                ->whereHas('scheduleMatch.match', function ($query) use ($cancelMatchTypes) {
                    if (!empty($cancelMatchTypes)) {
                        $query->where(function ($q) use ($cancelMatchTypes) {
                            $q->whereNull('status')
                                ->orWhereNotIn('status', $cancelMatchTypes);
                        });
                    }
                })
                ->get()->toArray();

            foreach ($unscheduledMatches as &$match) {
                $match['match'] = $match['schedule_match']['match'] ?? null;
                $match['time_slot_id'] = $match['id'];
            }

            return response()->json([
                'data' => $unscheduledMatches,
            ]);
        } catch (Exception $error) {
            Log::error("Error fetching unscheduled matches: " . $error->getMessage());
            return response()->json([
                'message' => 'Error fetching unscheduled matches: ' . $error->getMessage(),
            ], 500);
        }
    }

    public function deleteAllTimeSlotInTournament($tournamentId, $listMatchIds = [])
    {
        try {
            if (empty($listMatchIds)) {
                // delete all if $listMatchIds is empty
                ScheduleTimeSlot::where('tournament_id', $tournamentId)->delete();
            } else {
                // only delete ids in $listMatchIds
                ScheduleTimeSlot::where('tournament_id', $tournamentId)
                    ->whereHas('scheduleMatch', function ($query) use ($listMatchIds) {
                        $query->whereIn('match_id', $listMatchIds);
                    })
                    ->delete();
            }
            return true;
        } catch (Exception $error) {
            Log::error("Error clearing time slots: " . $error->getMessage());
            return false;
        }
    }

    public function clearScheduleOfTournament($tournament_id)
    {
        try {

            ScheduleTimeSlot::where('tournament_id', $tournament_id)->update([
                'location_id' => null,
                'start_time' => null,
                'end_time' => null,
            ]);

            ScheduleConfig::where('tournament_id', $tournament_id)->delete();

            (new ScheduleMatchController())->submitSchedule($tournament_id);

            return response()->json([
                'status' => 'success',
                'message' => 'Schedule cleared successfully',
            ]);
        } catch (Exception $error) {
            Log::error('error', [$error]);
            return response()->json([
                'message' => 'Error clearing schedule: ' . $error->getMessage(),
            ]);
        }
    }

    public function reIndexSlot($tournamentId, $locationId)
    {
        $groupMatchByDate = [];

        ScheduleTimeSlot::where('tournament_id', $tournamentId)
            ->where('location_id', $locationId)
            ->orderBy('start_time')
            ->get()
            ->each(function ($timeSlot, $index) use (&$groupMatchByDate) {
                $matchDate = Carbon::parse($timeSlot->start_time, 'UTC')->setTimezone(request()->header('X-Time-Zone') ?? 'UTC')->format('Y-m-d');
                if (!isset($groupMatchByDate[$matchDate])) {
                    $groupMatchByDate[$matchDate] = [];
                }

                $groupMatchByDate[$matchDate][] = $timeSlot;
            });

        foreach ($groupMatchByDate as $date => $timeSlots) {
            foreach ($timeSlots as $index => $timeSlot) {
                $timeSlot->slot_index = $index;
                $timeSlot->save();
            }
        }
    }

    public function generateTimeSlots($tournamentId, $locationId, $stageId, $beginDate, $beginTime, $endTime, $matchDuration, $breakDuration, $repeats, $timeZone, $foreUpdateConfig = false)
    {
        try {

            $scheduleConfigController = new ScheduleConfigController();
            // get last slot index of location in stage
            $lastSlotIndex = ScheduleTimeSlot::where('stage_id', $stageId)
                ->where('location_id', $locationId)
                ->orderBy('slot_index', 'desc')
                ->first()->slot_index ?? null;

            $results = [];

            $localTimezone = new DateTimeZone($timeZone);
            $utcTimezone = new DateTimeZone('UTC');
            $baseStartTime = new DateTime("$beginDate $beginTime", $localTimezone);
            $baseEndTime = new DateTime("$beginDate " . ($endTime ?: '23:59:59'), $localTimezone);

            $scheduleConfigController->updateOrCreateConfig(
                $tournamentId,
                $locationId,
                $baseStartTime->format('Y-m-d'),
                $baseStartTime->format('H:i:s'),
                $baseEndTime->format('H:i:s'),
                $matchDuration,
                $breakDuration,
                $timeZone,
                $foreUpdateConfig
            );

            $start = clone $baseStartTime;

            for ($i = 0; $i < $repeats; $i++) {

                $end = clone $start;
                $end->modify("+$matchDuration minutes");

                $startUTC = clone $start;
                $startUTC->setTimezone($utcTimezone);

                $endUTC = clone $end;
                $endUTC->setTimezone($utcTimezone);

                // Always store times in UTC for consistency
                $results[] = [
                    'tournament_id' => $tournamentId,
                    'location_id' => $locationId,
                    'stage_id' => $stageId,
                    'start_time' => $startUTC->format('Y-m-d H:i:s'),
                    'end_time' => $endUTC->format('Y-m-d H:i:s'),
                    'slot_index' => $lastSlotIndex ? $i + $lastSlotIndex + 1 : $i,
                ];

                $start = clone $end;

                if (!$this->checkNextMatchTime(clone $start, $baseEndTime, $matchDuration, $breakDuration) && $i < $repeats - 1) {
                    $baseStartTime->modify("+1 day");
                    $baseEndTime->modify("+1 day");
                    $start = clone $baseStartTime;
                    $scheduleConfigController->updateOrCreateConfig($tournamentId, $locationId, $baseStartTime->format('Y-m-d'), $baseStartTime->format('H:i:s'), $baseEndTime->format('H:i:s'), $matchDuration, $breakDuration, $timeZone, true);

                } else {
                    $start->modify("+$breakDuration minutes");
                }


            }

            $modifyIds = $this->createOrUpdateTimeSlot($results);

            $this->reIndexSlot($tournamentId, $locationId);

            return $modifyIds;
        } catch (Exception $error) {
            Log::error("Error generating time slots: " . $error->getMessage());
            return [];
        }
    }

    public function checkNextMatchTime(DateTime $startTime, DateTime $endTimeOfDate, $matchDuration, $breakDuration)
    {

        if ($startTime->modify("+$matchDuration minutes") > $endTimeOfDate) {
            return false;
        }

        return true;

    }

    public function createOrUpdateTimeSlot($timeslotData)
    {
        $modifyIds = [];

        foreach ($timeslotData as $timeSlot) {

            // update by time slot id
            if (isset($timeSlot['id'])) {
                $modifyIds[] = ScheduleTimeSlot::where('id', $timeSlot['id'])
                    ->update([
                        'location_id' => $timeSlot['location_id'],
                        'stage_id' => $timeSlot['stage_id'],
                        'start_time' => $timeSlot['start_time'],
                        'end_time' => $timeSlot['end_time'],
                        'slot_index' => $timeSlot['slot_index'],
                    ]);
            } else {

                // check if time slot exist
                $isExist = ScheduleTimeSlot::where('tournament_id', $timeSlot['tournament_id'])
                    ->where('location_id', $timeSlot['location_id'])
                    ->where('stage_id', $timeSlot['stage_id'])
                    ->where('slot_index', $timeSlot['slot_index'])
                    ->exists();

                if (!$isExist) {
                    // create new if not exists
                    $modifyIds[] = ScheduleTimeSlot::create([
                        'tournament_id' => $timeSlot['tournament_id'],
                        'location_id' => $timeSlot['location_id'],
                        'stage_id' => $timeSlot['stage_id'],
                        'start_time' => $timeSlot['start_time'],
                        'end_time' => $timeSlot['end_time'],
                        'slot_index' => $timeSlot['slot_index'],
                    ]);
                } else {
                    // update if exists
                    $modifyIds[] = ScheduleTimeSlot::where('tournament_id', $timeSlot['tournament_id'])
                        ->where('location_id', $timeSlot['location_id'])
                        ->where('stage_id', $timeSlot['stage_id'])
                        ->where('slot_index', $timeSlot['slot_index'])
                        ->update([
                            'start_time' => $timeSlot['start_time'],
                            'end_time' => $timeSlot['end_time'],
                        ]);
                }
            }
        }

        return $modifyIds;
    }

    public function getTimeSlots($tournamentId, $locationId)
    {
        return ScheduleTimeSlot::where('tournament_id', $tournamentId)
            ->where('location_id', $locationId)
            ->orderBy('start_time')
            ->get();
    }

    // regenerate by exists config method
    public function regenerateTimeSlots($tournamentId, $locationId, $date = null, $configId = null, $timeZone, $oldBeginDate = null, $oldBeginTime = null)
    {
        try {
            if ($configId) {
                Log::info('Config id ', [$configId]);

                $config = ScheduleConfig::find($configId);
            } else {
                $config = ScheduleConfig::where([
                    'tournament_id' => $tournamentId,
                    'location_id' => $locationId,
                    'begin_date' => $date,
                ])->first();
            }

            if (!$config) {
                throw new Exception('Schedule configuration not found');
            }

            $configBeginDate = Carbon::parse($config->begin_date, 'UTC')->format('Y-m-d');
            $configBeginTime = Carbon::parse($config->begin_time, 'UTC')->format('H:i:s');

            $listTimeSlots = ScheduleTimeSlot::where([
                'tournament_id' => $tournamentId,
                'location_id' => $locationId,
            ])->where(function ($query) use ($config, $oldBeginDate, $oldBeginTime, $timeZone) {
                $oldStartTime = null;

                if ($oldBeginDate && $oldBeginTime) {
                    $oldStartTime = Carbon::parse($oldBeginDate->format('Y-m-d') . ' ' . $oldBeginTime->format('H:i:s'), 'UTC');
                }
                $currentConfigStartTime = Carbon::parse($config->begin_date->format('Y-m-d') . ' ' . $config->begin_time->format('H:i:s'), 'UTC');

                $query->where(function ($q) use ($oldStartTime, $currentConfigStartTime, $timeZone) {
                    $startTime = $oldStartTime && $oldStartTime < $currentConfigStartTime ? $oldStartTime : $currentConfigStartTime;
                    $endDayOfLocalInUTC = $currentConfigStartTime->copy()->setTimezone($timeZone ?? 'UTC')->endOfDay()->utc();
                    $q->where('start_time', '>=', $startTime)
                        ->where('end_time', '<=', $endDayOfLocalInUTC);
                });
            })->with(['tournament', 'location', 'stage', 'scheduleMatch', 'break'])
                ->orderBy('start_time', 'asc')
                // ->orderByRaw('ISNULL(stage_id)') // true parameter makes nulls last
                // ->orderBy('stage_id', 'asc')
                ->orderBy('slot_index', 'asc')
                ->orderBy('type', 'desc')
                ->get();

            Log::info('List time slots count', [$listTimeSlots->count()]);
            Log::info('List time slots count', [$listTimeSlots]);

            // Config times are now stored in UTC, so parse them correctly
            $beginDateTime = $configBeginDate . ' ' . $configBeginTime;
            $beginDateTimeUTC = Carbon::parse($beginDateTime, 'UTC');

            $nextMatchTime = [
                'start_time' => $beginDateTimeUTC->copy(),
                'end_time' => $beginDateTimeUTC->copy()->addMinutes($config->match_duration),
            ];

            foreach ($listTimeSlots as $index => $timeSlot) {

                $timeSlot->slot_index = $index;

                if ($timeSlot->type == 'break') {
                    $startTime = $nextMatchTime['start_time']->copy();
                    $endTime = $startTime->copy()->addMinutes($timeSlot->break->break_durations);

                    // Store as UTC formatted strings
                    $timeSlot->start_time = $startTime->utc()->format('Y-m-d H:i:s');
                    $timeSlot->end_time = $endTime->utc()->format('Y-m-d H:i:s');

                    $nextMatchTime = [
                        'start_time' => $endTime->copy(),
                        'end_time' => $endTime->copy()->addMinutes($config->match_duration),
                    ];
                } else {
                    $startTime = $nextMatchTime['start_time']->copy();
                    $endTime = $nextMatchTime['end_time']->copy();

                    // Store as UTC formatted strings
                    $timeSlot->start_time = $startTime->utc()->format('Y-m-d H:i:s');
                    $timeSlot->end_time = $endTime->utc()->format('Y-m-d H:i:s');

                    $newStartTime = $endTime->copy()->addMinutes($config->break_match_duration);
                    $newEndTime = $newStartTime->copy()->addMinutes($config->match_duration);
                    $nextMatchTime = [
                        'start_time' => $newStartTime,
                        'end_time' => $newEndTime,
                    ];
                }
                $timeSlot->save();
            }

            (new ScheduleMatchController())->submitSchedule($tournamentId);

            return $this->getTimeSlots($tournamentId, $locationId);
        } catch (Exception $error) {
            Log::error("Error regenerating time slots: " . $error);
            throw new Exception('Error regenerating time slots: ' . $error->getMessage());
        }
    }

    public function deleteSchedule(Request $request)
    {
        try {
            $request->validate([
                'tournament_id' => 'required|integer|exists:tournaments,id',
                'location' => 'required|integer',
                'time_slot_ids' => 'required|array',
                'time_slot_ids.*' => 'integer|exists:schedule_time_slots,id',
                'config_id' => 'required|integer|exists:schedule_configs,id',
            ]);

            // update if type match
            ScheduleTimeSlot::where([
                'tournament_id' => $request->get('tournament_id'),
                'location_id' => $request->get('location'),
                'type' => 'match',
            ])->whereIn('id', $request->get('time_slot_ids'))
                ->update([
                    'location_id' => null,
                    'start_time' => null,
                    'end_time' => null,
                ]);

            ScheduleTimeSlot::where([
                'tournament_id' => $request->get('tournament_id'),
                'location_id' => $request->get('location'),
                'type' => 'break',
            ])->whereIn('id', $request->get('time_slot_ids'))
                ->delete();

            // delete schedule config
            ScheduleConfig::find($request->get('config_id'))->delete();

            (new ScheduleMatchController())->submitSchedule($request->get('tournament_id'));

            return response()->json([
                'message' => 'Deleted plan successfully.',
            ]);
        } catch (Exception $error) {
            Log::error("Error deleting plan:", [$error]);
            return response()->json([
                'message' => 'Error deleting plan: ' . $error->getMessage(),
            ], 500);
        }
    }

    public function unScheduleTimeSlot(Request $request)
    {
        // validate time_slot_id in request
        try {
            $request->validate([
                'time_slot_id' => 'required|integer|exists:schedule_time_slots,id',
                'config_id' => 'required|integer',
            ]);

            $currentTimeSlot = ScheduleTimeSlot::find($request->get('time_slot_id'));
            $locationId = $currentTimeSlot->location_id;
            $tournamentId = $currentTimeSlot->tournament_id;
            $type = $currentTimeSlot->type;

            if ($type == 'break') {
                ScheduleBreak::where('time_slot_id', $request->get('time_slot_id'))->delete();
                $currentTimeSlot->delete();
            } else {
                $currentTimeSlot->update([
                    'location_id' => null,
                    'start_time' => null,
                    'end_time' => null,
                ]);
            }

            $this->regenerateTimeSlots($tournamentId, $locationId, null, $request->get('config_id'), $request->header('X-Time-Zone'));



            return response()->json([
                'message' => 'Unscheduled time slot successfully.',
            ]);
        } catch (Exception $error) {
            Log::error("Error unscheduling match:", [$error]);
            return response()->json([
                'message' => $error->getMessage(),
            ], 500);
        }
    }

    // update schedule time by list new index
    public function updateLocationMatch(Request $request)
    {
        try {

            $request->validate([
                'new_index' => 'required|array',
                'location_id' => 'required|integer',
                'prev_location_id' => 'sometimes|integer|nullable',
                'tournament_id' => 'required|integer',
                'config_id' => 'required|integer',
                'prev_config_id' => 'sometimes|integer|nullable',
            ]);


            Log::info('new_index', [$request->get('new_index')]);
            Log::info('location_id', [$request->get('location_id')]);
            Log::info('prev_location_id', [$request->get('prev_location_id')]);
            Log::info('tournament_id', [$request->get('tournament_id')]);
            Log::info('config_id', [$request->get('config_id')]);
            Log::info('prev_config_id', [$request->get('prev_config_id')]);

            $config = ScheduleConfig::find($request->get('config_id'));

            // update new index
            foreach ($request->get('new_index') as $timeSlotId => $index) {
                ScheduleTimeSlot::find($timeSlotId)->update([
                    'location_id' => $request->get('location_id'),
                    'start_time' => Carbon::parse($config->begin_date->format('Y-m-d') . ' ' . $config->begin_time->format('H:i:s'), 'UTC'),
                    'end_time' => Carbon::parse($config->begin_date->format('Y-m-d') . ' ' . $config->begin_time->format('H:i:s'), 'UTC')->addMinutes($config->match_duration),
                    'slot_index' => $index,
                ]);

                Log::info('after update', [ScheduleTimeSlot::find($timeSlotId)]);
            }

            $timeZone = $request->header('X-Time-Zone');
            Log::info('$timeZone', [$timeZone]);

            // regenerate schedule time
            $this->regenerateTimeSlots(
                $request->get('tournament_id'),
                $request->get('location_id'),
                $request->get('date'),
                $request->get('config_id'),
                timeZone: $timeZone,
            );

            // if prev location not same as current location, regenerate schedule time for prev location
            if ($request->get('prev_location_id') != null && $request->get('prev_location_id') != $request->get('location_id')) {
                $this->regenerateTimeSlots(
                    $request->get('tournament_id'),
                    $request->get('prev_location_id'),
                    $request->get('date'),
                    $request->get('prev_config_id'),
                    timeZone: $timeZone
                );
            }

            return response()->json([
                'message' => 'Updated match schedule successfully.',
            ]);
        } catch (Exception $error) {
            Log::error("Error updating match schedule:", [$error]);
            return response()->json([
                'message' => $error->getMessage(),
            ], 500);
        }
    }

    public function autoInitTimeSlotByStageId($stageId)
    {
        try {
            $stageInfo = Stage::find($stageId);
            $tournamentId = $stageInfo->tournament->id;
            $stageMatches = StageMatch::where('stage_id', $stageId)->get();

            foreach ($stageMatches as $match) {
                $timeSlot = ScheduleTimeSlot::create([
                    'tournament_id' => $tournamentId,
                    'stage_id' => $stageId,
                    'match_id' => $match->id,
                    'location_id' => null,
                    'start_time' => null,
                    'end_time' => null,
                ]);
                ScheduleMatch::create([
                    'tournament_id' => $tournamentId,
                    'stage_id' => $stageId,
                    'match_id' => $match->id,
                    'time_slot_id' => $timeSlot->id,
                ]);
            }
        } catch (Exception $error) {
            Log::info('error', [$error]);
            throw new Exception("Something went wrong");
        }
    }

    public function regenerateForAllConfig($tourmentId, $timeZone)
    {
        try {
            $allConfig = ScheduleConfig::where('tournament_id', $tourmentId)->get();
            foreach ($allConfig as $config) {
                $this->regenerateTimeSlots(
                    $tourmentId,
                    $config->location_id,
                    $config->begin_date,
                    $config->id,
                    $timeZone,
                );
            }
        } catch (Exception $error) {
            Log::error('error', [$error]);
            throw new Exception("Something went wrong");
        }

    }

    public function manualAddTimeSlot($matchId, $tournamentId, $locationId, $startTime, $endTime, $stageId)
    {
        try {
            $startTime = Carbon::parse($startTime, 'UTC');
            $endTime = Carbon::parse($endTime, 'UTC');

            $scheduleConfigController = new ScheduleConfigController();
            $scheduleConfigController->updateOrCreateConfig(
                $tournamentId,
                $locationId,
                $startTime->copy()->format('Y-m-d'),
                $startTime->copy()->format('H:i:s'),
                $endTime->copy()->format('H:i:s'),
                Carbon::parse($endTime, 'UTC')->diffInMinutes(Carbon::parse($startTime, 'UTC')),
                0,
                'UTC',
            );

            $newTimeSlotId = ScheduleTimeSlot::create([
                'tournament_id' => $tournamentId,
                'location_id' => $locationId,
                'stage_id' => $stageId,
                'start_time' => $startTime,
                'end_time' => $endTime,
                'slot_index' => 0, // or any logic to determine index
            ])->id;

            ScheduleMatch::updateOrCreate(
                ['match_id' => $matchId, 'tournament_id' => $tournamentId],
                ['time_slot_id' => $newTimeSlotId, 'stage_id' => $stageId]
            );

            return true;


        } catch (Exception $error) {
            Log::error('Error adding manual time slot: ', [$error]);
            throw new Exception('Error adding manual time slot: ' . $error->getMessage());
        }
    }

    public function getScheduleDates(Request $request, $tournament_id)
    {
        try {

            $timezone = request()->header('X-Time-Zone') ?? 'UTC';

            $listDates = [];

            $configs = ScheduleConfig::where('tournament_id', $tournament_id)
                ->orderBy('begin_date', 'asc')
                ->orderBy('begin_time', 'asc')
                ->get();

            foreach ($configs as $config) {
                $date = Carbon::parse($config->begin_date . " " . $config->begin_time, 'UTC')->setTimezone($timezone)->format('Y-m-d');
                if (!in_array($date, $listDates)) {
                    $listDates[] = $date;
                }
            }

            return response()->json([

                'data' => $listDates,
            ]);
        } catch (Exception $error) {
            Log::error('Error getting schedule dates: ', [$error]);
            return response()->json([
                'message' => 'Error getting schedule dates: ' . $error->getMessage(),
            ], 500);
        }

    }
}
