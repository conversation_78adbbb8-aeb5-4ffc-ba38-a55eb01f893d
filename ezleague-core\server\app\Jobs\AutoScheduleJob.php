<?php

namespace App\Jobs;

use App\Http\Controllers\ScheduleMatchController;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AutoScheduleJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected array $requestData;
    protected string $jobId;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     */
    public int $timeout = 1800; // 30 minutes

    /**
     * Create a new job instance.
     */
    public function __construct(array $requestData, string $jobId = null)
    {
        $this->requestData = $requestData;
        $this->jobId = $jobId ?? uniqid('auto_schedule_');

        // Determine queue based on complexity
        $queueName = $this->determineQueue();
        $this->onQueue($queueName);
        $this->onConnection('database');

        Log::info("AutoScheduleJob created", [
            'job_id' => $this->jobId,
            'tournament_id' => $this->requestData['tournament_id'] ?? 'unknown',
            'queue' => $queueName
        ]);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $startTime = microtime(true);

        Log::info("AutoScheduleJob started", [
            'job_id' => $this->jobId,
            'tournament_id' => $this->requestData['tournament_id'] ?? 'unknown'
        ]);

        try {
            // Validate required data
            $requiredFields = ['tournament_id', 'list_location_ids', 'begin_date', 'begin_time', 'match_duration', 'break_duration'];
            foreach ($requiredFields as $field) {
                if (!isset($this->requestData[$field])) {
                    throw new Exception("Missing required field: {$field}");
                }
            }

            $controller = new ScheduleMatchController();

            // Create request with all required fields
            $request = new \Illuminate\Http\Request();

            $requestData = array_merge([
                'tournament_id' => $this->requestData['tournament_id'],
                'list_location_ids' => $this->requestData['list_location_ids'],
                'begin_date' => $this->requestData['begin_date'],
                'begin_time' => $this->requestData['begin_time'],
                'match_duration' => $this->requestData['match_duration'],
                'break_duration' => $this->requestData['break_duration'],
                'stage_id' => $this->requestData['stage_id'] ?? -1,
                'nums_of_referees' => $this->requestData['nums_of_referees'] ?? 0,
                'list_referee_ids' => $this->requestData['list_referee_ids'] ?? [],
                'list_group_names' => $this->requestData['list_group_names'] ?? [],
                'recreate_all_stage' => $this->requestData['recreate_all_stage'] ?? false,
                'chunk_size' => 25,
                'force_sync' => true // Force sync processing in job
            ], $this->requestData);

            // Handle optional end_time
            if (isset($this->requestData['end_time']) && !empty($this->requestData['end_time'])) {
                $requestData['end_time'] = $this->requestData['end_time'];
            }

            $request->merge($requestData);
            $request->headers->set('X-Time-Zone', $this->requestData['timezone'] ?? 'UTC');

            Log::info("AutoScheduleJob processing", [
                'job_id' => $this->jobId,
                'tournament_id' => $requestData['tournament_id'],
                'location_count' => count($requestData['list_location_ids']),
                'stage_id' => $requestData['stage_id']
            ]);

            // Process the scheduling
            $response = $controller->scheduleMatches($request);

            $executionTime = microtime(true) - $startTime;

            if ($response->getStatusCode() === 200) {
                Log::info("AutoScheduleJob completed successfully", [
                    'job_id' => $this->jobId,
                    'tournament_id' => $this->requestData['tournament_id'],
                    'execution_time' => round($executionTime, 2) . 's'
                ]);
            } else {
                $responseData = json_decode($response->getContent(), true);
                $errorMessage = $responseData['message'] ?? 'Unknown error';
                throw new Exception("Schedule processing failed with status {$response->getStatusCode()}: {$errorMessage}");
            }

        } catch (Exception $e) {
            $executionTime = microtime(true) - $startTime;

            Log::error("AutoScheduleJob failed", [
                'job_id' => $this->jobId,
                'tournament_id' => $this->requestData['tournament_id'] ?? 'unknown',
                'error' => $e->getMessage(),
                'execution_time' => round($executionTime, 2) . 's',
                'attempt' => $this->attempts()
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(Exception $exception): void
    {
        Log::error("AutoScheduleJob failed permanently", [
            'job_id' => $this->jobId,
            'tournament_id' => $this->requestData['tournament_id'] ?? 'unknown',
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts()
        ]);
    }

    /**
     * Determine the appropriate queue based on tournament complexity
     */
    private function determineQueue(): string
    {
        try {
            $tournamentId = $this->requestData['tournament_id'] ?? null;

            if (!$tournamentId) {
                Log::warning("No tournament_id provided, using default queue");
                return 'default';
            }

            // Use direct database query
            $totalMatches = DB::table('stage_matches')
                ->join('stages', 'stages.id', '=', 'stage_matches.stage_id')
                ->where('stages.tournament_id', $tournamentId)
                ->count();

            Log::info("AutoScheduleJob queue determination", [
                'tournament_id' => $tournamentId,
                'total_matches' => $totalMatches
            ]);

            // Route to different queues based on complexity
            if ($totalMatches > 1000) {
                return 'heavy-scheduling';
            } elseif ($totalMatches > 100) {
                return 'medium-scheduling';
            } elseif ($totalMatches > 0) {
                return 'light-scheduling';
            } else {
                return 'default';
            }
        } catch (Exception $e) {
            Log::warning("Could not determine queue complexity, using default", [
                'tournament_id' => $this->requestData['tournament_id'] ?? 'unknown',
                'error' => $e->getMessage()
            ]);
            return 'default';
        }
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return [
            'auto-schedule',
            'tournament:' . ($this->requestData['tournament_id'] ?? 'unknown'),
            'job:' . $this->jobId
        ];
    }
}
