<?php

namespace App\Jobs;

use App\Http\Controllers\ScheduleMatchController;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProcessScheduleMatchesJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected array $requestData;
    protected string $jobId;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     */
    public int $timeout = 1800; // 30 minutes

    /**
     * Create a new job instance.
     */
    public function __construct(array $requestData, string $jobId = null)
    {
        $this->requestData = $requestData;
        $this->jobId = $jobId ?? uniqid('schedule_job_');

        // Set queue based on tournament size for better resource management
        $queueName = $this->determineQueue();
        $this->onQueue($queueName);

        // Ensure we use the database connection for all queues
        $this->onConnection('database');

        Log::info("Job created for queue: {$queueName}", [
            'job_id' => $this->jobId,
            'tournament_id' => $this->requestData['tournament_id'] ?? 'unknown'
        ]);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $startTime = microtime(true);

        Log::info("Starting background schedule processing", [
            'job_id' => $this->jobId,
            'tournament_id' => $this->requestData['tournament_id'],
            'queue' => $this->queue
        ]);

        try {
            $controller = new ScheduleMatchController();

            // Create a mock request object from the data
            $request = new \Illuminate\Http\Request();
            $request->merge($this->requestData);
            $request->headers->set('X-Time-Zone', $this->requestData['timezone'] ?? 'UTC');

            // Process the scheduling
            $response = $controller->scheduleMatches($request);

            $executionTime = microtime(true) - $startTime;

            if ($response->getStatusCode() === 200) {
                Log::info("Background schedule processing completed successfully", [
                    'job_id' => $this->jobId,
                    'tournament_id' => $this->requestData['tournament_id'],
                    'execution_time' => round($executionTime, 2) . 's'
                ]);

                // Optionally notify users or update status
                $this->notifyCompletion(true, $executionTime);
            } else {
                throw new Exception('Schedule processing failed with status: ' . $response->getStatusCode());
            }

        } catch (Exception $e) {
            $executionTime = microtime(true) - $startTime;

            Log::error("Background schedule processing failed", [
                'job_id' => $this->jobId,
                'tournament_id' => $this->requestData['tournament_id'],
                'error' => $e->getMessage(),
                'execution_time' => round($executionTime, 2) . 's',
                'attempt' => $this->attempts()
            ]);

            $this->notifyCompletion(false, $executionTime, $e->getMessage());

            // Re-throw to trigger retry mechanism
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(Exception $exception): void
    {
        Log::error("Schedule processing job failed permanently", [
            'job_id' => $this->jobId,
            'tournament_id' => $this->requestData['tournament_id'],
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts()
        ]);

        $this->notifyCompletion(false, 0, $exception->getMessage(), true);
    }

    /**
     * Determine the appropriate queue based on tournament complexity
     */
    private function determineQueue(): string
    {
        try {
            $tournamentId = $this->requestData['tournament_id'] ?? null;

            if (!$tournamentId) {
                Log::warning("No tournament_id provided, using default queue");
                return 'default';
            }

            // Use direct database query to avoid model loading issues during job creation
            $totalMatches = DB::table('stage_matches')
                ->join('stages', 'stages.id', '=', 'stage_matches.stage_id')
                ->where('stages.tournament_id', $tournamentId)
                ->count();

            Log::info("Queue determination", [
                'tournament_id' => $tournamentId,
                'total_matches' => $totalMatches
            ]);

            // Route to different queues based on complexity
            if ($totalMatches > 1000) {
                return 'heavy-scheduling';
            } elseif ($totalMatches > 100) {
                return 'medium-scheduling';
            } elseif ($totalMatches > 0) {
                return 'light-scheduling';
            } else {
                return 'default';
            }
        } catch (Exception $e) {
            Log::warning("Could not determine queue complexity, using default", [
                'tournament_id' => $this->requestData['tournament_id'] ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 'default';
        }
    }

    /**
     * Notify about job completion (success or failure)
     */
    private function notifyCompletion(bool $success, float $executionTime, string $error = null, bool $permanentFailure = false): void
    {
        // This could be extended to:
        // - Send email notifications
        // - Update database status
        // - Trigger webhooks
        // - Send push notifications

        $status = $success ? 'completed' : ($permanentFailure ? 'failed' : 'retrying');

        Log::info("Schedule job notification", [
            'job_id' => $this->jobId,
            'tournament_id' => $this->requestData['tournament_id'],
            'status' => $status,
            'execution_time' => round($executionTime, 2) . 's',
            'error' => $error
        ]);

        // TODO: Implement actual notification mechanisms
        // - Database status updates
        // - Email/SMS notifications
        // - WebSocket real-time updates
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return [
            'schedule-matches',
            'tournament:' . $this->requestData['tournament_id'],
            'job:' . $this->jobId
        ];
    }
}
