<?php

namespace App\Jobs;

use App\Http\Controllers\ScheduleMatchController;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProcessScheduleMatchesJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected array $requestData;
    protected string $jobId;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     */
    public int $timeout = 1800; // 30 minutes

    /**
     * Create a new job instance.
     */
    public function __construct(array $requestData, string $jobId = null)
    {
        $this->requestData = $requestData;
        $this->jobId = $jobId ?? uniqid('schedule_job_');

        // Set queue based on tournament size for better resource management
        $queueName = $this->determineQueue();
        $this->onQueue($queueName);

        // Ensure we use the database connection for all queues
        $this->onConnection('database');

        Log::info("Job created for queue: {$queueName}", [
            'job_id' => $this->jobId,
            'tournament_id' => $this->requestData['tournament_id'] ?? 'unknown'
        ]);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $startTime = microtime(true);

        Log::info("Starting background schedule processing", [
            'job_id' => $this->jobId,
            'tournament_id' => $this->requestData['tournament_id'],
            'queue' => $this->queue
        ]);

        try {
            // Validate required data
            $requiredFields = ['tournament_id', 'list_location_ids', 'begin_date', 'begin_time', 'match_duration', 'break_duration'];
            foreach ($requiredFields as $field) {
                if (!isset($this->requestData[$field])) {
                    throw new Exception("Missing required field: {$field}");
                }
            }

            $controller = new ScheduleMatchController();

            // Create a properly validated request object
            $request = new \Illuminate\Http\Request();

            // Set all required fields with defaults
            $requestData = array_merge([
                'tournament_id' => $this->requestData['tournament_id'],
                'list_location_ids' => $this->requestData['list_location_ids'],
                'begin_date' => $this->requestData['begin_date'],
                'begin_time' => $this->requestData['begin_time'],
                'match_duration' => $this->requestData['match_duration'],
                'break_duration' => $this->requestData['break_duration'],
                'stage_id' => $this->requestData['stage_id'] ?? -1,
                'nums_of_referees' => $this->requestData['nums_of_referees'] ?? 0,
                'list_referee_ids' => $this->requestData['list_referee_ids'] ?? [],
                'list_group_names' => $this->requestData['list_group_names'] ?? [],
                'recreate_all_stage' => $this->requestData['recreate_all_stage'] ?? false,
                'chunk_size' => 25, // Small chunks for background processing
                'force_sync' => true // CRITICAL: Force sync processing in job
            ], $this->requestData);

            // Handle optional end_time - only include if provided and valid
            if (isset($this->requestData['end_time']) && !empty($this->requestData['end_time'])) {
                $requestData['end_time'] = $this->requestData['end_time'];
            }

            $request->merge($requestData);
            $request->headers->set('X-Time-Zone', $this->requestData['timezone'] ?? 'UTC');

            Log::info("Processing schedule with validated data", [
                'job_id' => $this->jobId,
                'tournament_id' => $requestData['tournament_id'],
                'location_count' => count($requestData['list_location_ids']),
                'stage_id' => $requestData['stage_id']
            ]);

            // Process the scheduling
            $response = $controller->scheduleMatches($request);

            $executionTime = microtime(true) - $startTime;

            if ($response->getStatusCode() === 200) {
                Log::info("Background schedule processing completed successfully", [
                    'job_id' => $this->jobId,
                    'tournament_id' => $this->requestData['tournament_id'],
                    'execution_time' => round($executionTime, 2) . 's'
                ]);

                $this->notifyCompletion(true, $executionTime);
            } else {
                $responseData = json_decode($response->getContent(), true);
                $errorMessage = $responseData['message'] ?? 'Unknown error';
                throw new Exception("Schedule processing failed with status {$response->getStatusCode()}: {$errorMessage}");
            }

        } catch (Exception $e) {
            $executionTime = microtime(true) - $startTime;

            Log::error("Background schedule processing failed", [
                'job_id' => $this->jobId,
                'tournament_id' => $this->requestData['tournament_id'],
                'error' => $e->getMessage(),
                'execution_time' => round($executionTime, 2) . 's',
                'attempt' => $this->attempts()
            ]);

            $this->notifyCompletion(false, $executionTime, $e->getMessage());

            // Re-throw to trigger retry mechanism
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(Exception $exception): void
    {
        Log::error("Schedule processing job failed permanently", [
            'job_id' => $this->jobId,
            'tournament_id' => $this->requestData['tournament_id'],
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts()
        ]);

        $this->notifyCompletion(false, 0, $exception->getMessage(), true);
    }

    /**
     * Determine the appropriate queue based on tournament complexity
     */
    private function determineQueue(): string
    {
        try {
            $tournamentId = $this->requestData['tournament_id'] ?? null;

            if (!$tournamentId) {
                Log::warning("No tournament_id provided, using default queue");
                return 'default';
            }

            // Use direct database query to avoid model loading issues during job creation
            $totalMatches = DB::table('stage_matches')
                ->join('stages', 'stages.id', '=', 'stage_matches.stage_id')
                ->where('stages.tournament_id', $tournamentId)
                ->count();

            Log::info("Queue determination", [
                'tournament_id' => $tournamentId,
                'total_matches' => $totalMatches
            ]);

            // Route to different queues based on complexity
            if ($totalMatches > 1000) {
                return 'heavy-scheduling';
            } elseif ($totalMatches > 100) {
                return 'medium-scheduling';
            } elseif ($totalMatches > 0) {
                return 'light-scheduling';
            } else {
                return 'default';
            }
        } catch (Exception $e) {
            Log::warning("Could not determine queue complexity, using default", [
                'tournament_id' => $this->requestData['tournament_id'] ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 'default';
        }
    }

    /**
     * Notify about job completion (success or failure)
     */
    private function notifyCompletion(bool $success, float $executionTime, string $error = null, bool $permanentFailure = false): void
    {
        // This could be extended to:
        // - Send email notifications
        // - Update database status
        // - Trigger webhooks
        // - Send push notifications

        $status = $success ? 'completed' : ($permanentFailure ? 'failed' : 'retrying');

        Log::info("Schedule job notification", [
            'job_id' => $this->jobId,
            'tournament_id' => $this->requestData['tournament_id'],
            'status' => $status,
            'execution_time' => round($executionTime, 2) . 's',
            'error' => $error
        ]);

        // TODO: Implement actual notification mechanisms
        // - Database status updates
        // - Email/SMS notifications
        // - WebSocket real-time updates
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return [
            'schedule-matches',
            'tournament:' . $this->requestData['tournament_id'],
            'job:' . $this->jobId
        ];
    }
}
