<?php
namespace App\Http\Controllers;

use App\Jobs\ProcessScheduleMatchesJob;
use App\Models\ScheduleMatch;
use App\Models\ScheduleTimeSlot;
use App\Models\SeasonReferee;
use App\Models\Stage;
use App\Models\StageMatch;
use App\Models\StageMatchReferee;
use App\Utils\PerformanceMonitor;
use App\Utils\TimezoneHelper;
use Carbon\Carbon;
use DateTime;
use DateTimeZone;
use Error;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ScheduleMatchController extends Controller
{

    private $timeSlotController;
    private $tournamentController;
    private $scheduleConfigController;

    public function __construct()
    {
        $this->timeSlotController = new ScheduleTimeSlotController();
        $this->stageController = new StageController();
        $this->tournamentController = new TournamentController();
        $this->scheduleConfigController = new ScheduleConfigController();
    }

    public function scheduleMatches(Request $request)
    {
        PerformanceMonitor::startTimer('schedule_matches_total');

        try {
            $request->validate([
                'tournament_id' => 'required|integer|exists:tournaments,id',
                'list_location_ids' => 'required|array',
                'list_location_ids.*' => 'integer|exists:locations,id',
                'stage_id' => 'sometimes|integer',
                'begin_date' => 'required|date_format:Y-m-d',
                'begin_time' => 'required|date_format:H:i',
                'end_time' => 'sometimes|date_format:H:i',
                'match_duration' => 'required|integer|min:1',
                'break_duration' => 'required|integer|min:0',
                'nums_of_referees' => 'sometimes|integer',
                'list_referee_ids' => 'sometimes|array',
                'list_referee_ids.*' => 'sometimes|integer|exists:season_referees,id',
                'list_group_names' => 'sometimes|array',
                'recreate_all_stage' => 'sometimes|boolean',
                'chunk_size' => 'sometimes|integer|min:10|max:1000', // Add chunking support
            ]);

            // Optimize: Use direct model queries with eager loading instead of controller methods
            $tournamentId = $request->get('tournament_id');
            $tournamentInfo = DB::table('tournaments')->where('id', $tournamentId)->first();

            if (!$tournamentInfo) {
                return response()->json(['message' => 'Tournament not found'], 404);
            }

            // Optimize: Eager load stages with their matches count for better performance
            $listStages = Stage::where('tournament_id', $tournamentId)
                ->withCount([
                    'matches' => function ($query) {
                        $cancelMatchTypes = config('constants.cancel_match_types');
                        $query->where(function ($q) use ($cancelMatchTypes) {
                            $q->whereNotIn('status', $cancelMatchTypes)->orWhereNull('status');
                        });
                    }
                ])
                ->get();

            $stageId = $request->get('stage_id', -1); // stage_id = -1 => schedule for all stages
            $listGroupNames = $request->get('list_group_names', []);
            $timeZone = TimezoneHelper::getRequestTimezone(); // Optimized timezone handling
            $chunkSize = $request->get('chunk_size', 100); // Default chunk size

            if ($stageId == -1) {
                $this->scheduleConfigController->clearAllConfigs($request->get('tournament_id'));
            }

            if ($tournamentInfo->type == config('constants.tournament_types.league')) {

                // require stage id for league
                if (!$stageId) {
                    return response()->json([
                        'message' => 'Stage ID is required for league tournaments.',
                    ], 400);
                }
                $countStageMatches = StageMatch::where('stage_id', $stageId)->count();

                // check if stage do not have any matches then auto generate matches
                if ($countStageMatches === 0) {
                    return response()->json([
                        'message' => 'Please generate matches for this tournament before scheduling.',
                    ], 400);
                }


                $this->planLeagueAndGroup(
                    $tournamentInfo->id,
                    $request->get('stage_id'),
                    $request->get('list_location_ids'),
                    $timeZone,
                    $request->get('begin_date'),
                    $request->get('begin_time'),
                    $request->get('end_time'),
                    $request->get('match_duration'),
                    $request->get('break_duration'),
                    [],
                    $chunkSize
                );

                // assign referees
                if ($request->has('nums_of_referees') && $request->has('list_referee_ids')) {
                    $this->assignRefereesToMatches(
                        $request->get('tournament_id'),
                        $request->get('stage_id'),
                        $request->get('nums_of_referees'),
                        $request->get('list_referee_ids')
                    );
                }
            } elseif ($tournamentInfo->type == config('constants.tournament_types.groups_knockouts')) {
                $listKnockoutStages = [];
                $groupStageId = null;

                foreach ($listStages as $stage) {
                    if ($stage->type == config('constants.tournament_types.groups')) {

                        $groupStageId = $stage->id;

                        $countStageMatches = StageMatch::where('stage_id', $groupStageId)->count();

                        // check if stage do not have any matches then auto generate matches
                        if ($countStageMatches === 0) {
                            return response()->json([
                                'message' => 'Please generate matches for this tournament before scheduling.',
                            ], 400);
                        }


                        if ($stageId == -1 || $stageId == $stage->id) {
                            $this->planLeagueAndGroup(
                                $tournamentInfo->id,
                                $stage->id,
                                $request->get('list_location_ids'),
                                $timeZone,
                                $request->get('begin_date'),
                                $request->get('begin_time'),
                                $request->get('end_time'),
                                $request->get('match_duration'),
                                $request->get('break_duration'),
                                $listGroupNames,
                                $chunkSize
                            );
                        }
                    }

                    if ($stage->type == config('constants.tournament_types.knockout')) {
                        if ($stageId == -1 || $stageId == $stage->id) {
                            $listKnockoutStages[] = $stage->id;
                        }
                    }
                }

                $lastMatchTime = $this->getLastMatchTimeOfStage($request->get('tournament_id'), $groupStageId);
                $lastMatchTime = Carbon::parse($lastMatchTime, $timeZone);

                if (
                    !$this->timeSlotController->checkNextMatchTime( // check if there is enough time to schedule knockout matches
                        $lastMatchTime->copy()->addMinutes($request->get('break_duration')),
                        Carbon::parse($lastMatchTime->copy()->format('Y-m-d') . ' ' . $request->get('end_time'), $timeZone),
                        $request->get('match_duration'),
                        $request->get('break_duration')
                    )
                ) {
                    $localTimezone = new DateTimeZone($timeZone);
                    $baseStartDate = (new DateTime($lastMatchTime->copy()->format('Y-m-d'), $localTimezone))->modify("+1 day");
                    $baseStartTime = (new DateTime($lastMatchTime->copy()->format('Y-m-d') . " " . $request->get('begin_time'), $localTimezone))->modify("+1 day");
                    $baseEndTime = (new DateTime($lastMatchTime->copy()->format('Y-m-d') . " " . $request->get('end_time'), $localTimezone))->modify("+1 day");

                    $this->planKnockout(
                        $request->get('tournament_id'),
                        $groupStageId,
                        $listKnockoutStages,
                        $request->get('list_location_ids'),
                        $request->get('match_duration'),
                        $request->get('break_duration'),
                        $baseStartDate->format('Y-m-d'),
                        $baseStartTime->format('H:i:s'), // begin time of next day
                        $baseEndTime->format('H:i:s'),
                        $timeZone,
                    );
                } else {

                    $this->planKnockout(
                        $request->get('tournament_id'),
                        $groupStageId,
                        $listKnockoutStages,
                        $request->get('list_location_ids'),
                        $request->get('match_duration'),
                        $request->get('break_duration'),
                        $stageId != -1 ? $request->get('begin_date') : null,
                        $stageId != -1 ? $request->get('begin_time') : null,
                        $request->get('end_time'),
                        $timeZone,
                    );
                }

                // assign referee for knockout stage
                if ($request->has('nums_of_referees') && $request->has('list_referee_ids')) {

                    foreach ($listStages as $stage) {
                        if ($stageId == -1 || $stageId == $stage->id) {
                            $this->assignRefereesToMatches(
                                $request->get('tournament_id'),
                                $stage->id,
                                $request->get('nums_of_referees'),
                                $request->get('list_referee_ids')
                            );
                        }
                    }
                }
            }

            $this->timeSlotController->regenerateForAllConfig($tournamentId, $timeZone);

            $this->submitSchedule($tournamentId);

            // End performance monitoring
            $performanceMetrics = PerformanceMonitor::endTimer('schedule_matches_total', [
                'tournament_id' => $tournamentId,
                'chunk_size' => $chunkSize,
                'timezone' => $timeZone
            ]);

            return response()->json([
                'message' => 'Schedule matches successfully',
                'performance' => [
                    'execution_time' => $performanceMetrics['execution_time'] ?? 0,
                    'memory_usage_mb' => $performanceMetrics['memory_used'] ?? 0,
                    'queries_executed' => $performanceMetrics['queries_executed'] ?? 0
                ]
            ], 200);

        } catch (Exception $error) {
            // End performance monitoring with error context
            PerformanceMonitor::endTimer('schedule_matches_total', [
                'tournament_id' => $request->get('tournament_id'),
                'error' => $error->getMessage(),
                'failed' => true
            ]);

            return response()->json([
                'message' => 'Error scheduling matches: ' . $error->getMessage(),
            ], 500);
        }
    }

    /**
     * Async version of scheduleMatches for large tournaments
     * Dispatches the scheduling process to a background job
     */
    public function scheduleMatchesAsync(Request $request)
    {
        try {
            $request->validate([
                'tournament_id' => 'required|integer|exists:tournaments,id',
                'list_location_ids' => 'required|array',
                'list_location_ids.*' => 'integer|exists:locations,id',
                'stage_id' => 'sometimes|integer',
                'begin_date' => 'required|date_format:Y-m-d',
                'begin_time' => 'required|date_format:H:i',
                'end_time' => 'sometimes|date_format:H:i',
                'match_duration' => 'required|integer|min:1',
                'break_duration' => 'required|integer|min:0',
                'nums_of_referees' => 'sometimes|integer',
                'list_referee_ids' => 'sometimes|array',
                'list_referee_ids.*' => 'sometimes|integer|exists:season_referees,id',
                'list_group_names' => 'sometimes|array',
                'recreate_all_stage' => 'sometimes|boolean',
                'chunk_size' => 'sometimes|integer|min:10|max:1000',
            ]);

            $tournamentId = $request->get('tournament_id');
            $timeZone = $request->header('X-Time-Zone') ?? 'UTC';

            // Estimate complexity to determine if async processing is beneficial
            $totalMatches = $this->estimateTournamentComplexity($tournamentId, $request->get('stage_id', -1));

            // For smaller tournaments, process synchronously
            if ($totalMatches < 50) {
                return $this->scheduleMatches($request);
            }

            $jobId = uniqid('schedule_' . $tournamentId . '_');

            // Prepare job data
            $jobData = $request->all();
            $jobData['timezone'] = $timeZone;

            // Dispatch to background job
            ProcessScheduleMatchesJob::dispatch($jobData, $jobId);

            Log::info("Dispatched async schedule processing", [
                'job_id' => $jobId,
                'tournament_id' => $tournamentId,
                'estimated_matches' => $totalMatches
            ]);

            return response()->json([
                'message' => 'Schedule processing started in background',
                'job_id' => $jobId,
                'estimated_matches' => $totalMatches,
                'status' => 'processing'
            ], 202); // HTTP 202 Accepted

        } catch (Exception $error) {
            Log::error("Error dispatching async schedule processing", [
                'tournament_id' => $request->get('tournament_id'),
                'error' => $error->getMessage()
            ]);

            return response()->json([
                'message' => 'Error starting schedule processing: ' . $error->getMessage(),
            ], 500);
        }
    }

    /**
     * Estimate tournament complexity for async processing decisions
     */
    private function estimateTournamentComplexity(int $tournamentId, int $stageId): int
    {
        $cancelMatchTypes = config('constants.cancel_match_types');

        if ($stageId === -1) {
            // All stages
            return StageMatch::whereHas('stage', function ($query) use ($tournamentId) {
                $query->where('tournament_id', $tournamentId);
            })
                ->where(function ($query) use ($cancelMatchTypes) {
                    $query->whereNotIn('status', $cancelMatchTypes)->orWhereNull('status');
                })
                ->count();
        } else {
            // Specific stage
            return StageMatch::where('stage_id', $stageId)
                ->where(function ($query) use ($cancelMatchTypes) {
                    $query->whereNotIn('status', $cancelMatchTypes)->orWhereNull('status');
                })
                ->count();
        }
    }

    public function getLastMatchTimeOfStage($tournamentId, $groupStageId)
    {
        $lastMatchTime = ScheduleTimeSlot::where('tournament_id', $tournamentId)
            ->where('stage_id', $groupStageId)
            ->orderBy('end_time', 'desc')
            ->first(['end_time']);

        // Parse the UTC time correctly and convert to the specified timezone
        return $lastMatchTime ? Carbon::parse($lastMatchTime['end_time'], 'UTC') : null;

    }

    public function planLeagueAndGroup(
        int $tournamentId,
        int $stageId,
        array $listLocationIds,
        string|null $timeZone,
        string $beginDate,
        string $beginTime,
        string|null $endTime = null,
        int $matchDuration,
        int $breakMatchDuration,
        array $listGroupNames = [],
        int $chunkSize = 100
    ) {
        $methodStartTime = microtime(true);

        try {
            // Optimize: Use direct query instead of find() to avoid loading unnecessary data
            $stageInfo = Stage::select('id', 'type', 'tournament_id')->find($stageId);

            if (!$stageInfo) {
                throw new Exception("Stage not found: {$stageId}");
            }

            // Get cancel match types from constants to filter out cancelled matches
            $cancelMatchTypes = config('constants.cancel_match_types');

            // Optimize: Build more efficient query with proper indexing hints
            $stageMatchesQuery = StageMatch::select([
                'stage_matches.id',
                'stage_matches.stage_id',
                'stage_matches.home_team_id',
                'stage_matches.away_team_id',
                'stage_matches.status',
                'stage_matches.round_name',
                'stage_matches.round_level',
                'stage_matches.order',
                DB::raw('COALESCE(home_teams.group, away_teams.group) as group_name')
            ])
                ->from('stage_matches')
                ->leftJoin('stage_teams as home_teams', function ($join) {
                    $join->on('home_teams.team_id', '=', 'stage_matches.home_team_id')
                        ->on('home_teams.stage_id', '=', 'stage_matches.stage_id');
                })
                ->leftJoin('stage_teams as away_teams', function ($join) {
                    $join->on('away_teams.team_id', '=', 'stage_matches.away_team_id')
                        ->on('away_teams.stage_id', '=', 'stage_matches.stage_id');
                })
                ->where('stage_matches.stage_id', $stageId)
                ->where(function ($query) use ($cancelMatchTypes) {
                    $query->whereNotIn('stage_matches.status', $cancelMatchTypes)
                        ->orWhereNull('stage_matches.status');
                })
                ->orderBy('stage_matches.order')
                ->orderBy('stage_matches.id');

            // Apply group filtering at database level for better performance
            if (!empty($listGroupNames)) {
                $stageMatchesQuery->where(function ($query) use ($listGroupNames) {
                    $query->whereIn('home_teams.group', $listGroupNames)
                        ->orWhereIn('away_teams.group', $listGroupNames);
                });
            }

            $stageMatches = $stageMatchesQuery->get();

            $willPlanMatches = [];

            if ($stageInfo->type == config('constants.tournament_types.league')) {
                $this->timeSlotController->deleteAllTimeSlotInTournament($tournamentId);
                $willPlanMatches = $stageMatches->toArray();
            } elseif ($stageInfo->type == config('constants.tournament_types.groups')) {

                if (count($listGroupNames) == 0) {
                    // plan all if $listGroupNames is empty
                    $this->timeSlotController->deleteAllTimeSlotInTournament($tournamentId);
                    $willPlanMatches = $stageMatches->toArray();
                } else {

                    $listMatchIds = [];
                    // filter matches by group names
                    foreach ($stageMatches as $match) {
                        if (in_array($match['group_name'], $listGroupNames)) {
                            $willPlanMatches[] = $match;
                            $listMatchIds[] = $match->id;
                        }
                    }
                    // Clear time slots for the matches that will be planned
                    $this->timeSlotController->deleteAllTimeSlotInTournament($tournamentId, $listMatchIds);
                }
            }

            $locationMatches = $this->divideMatchesByLocation($willPlanMatches, $listLocationIds, $stageInfo->type)['locationMatches'];

            $lastDate = null;
            $totalMatches = array_sum(array_map('count', $locationMatches));

            Log::info("Processing {$totalMatches} matches across " . count($locationMatches) . " locations for stage {$stageId}");

            foreach ($locationMatches as $locationId => $matches) {
                $matchCount = count($matches);

                if ($matchCount === 0) {
                    continue;
                }

                Log::info("Generating {$matchCount} time slots for location {$locationId}");

                // generate time slots
                $timeSlots = $this->timeSlotController->generateTimeSlots(
                    $tournamentId,
                    $locationId,
                    $stageId,
                    $beginDate,
                    $beginTime,
                    $endTime,
                    $matchDuration,
                    $breakMatchDuration,
                    $matchCount,
                    $timeZone,
                    true
                );

                if (count($timeSlots) !== $matchCount) {
                    throw new Exception("Time slot count mismatch: expected {$matchCount}, got " . count($timeSlots));
                }

                // Optimize: Process matches in chunks to avoid memory issues
                $matchChunks = array_chunk($matches, $chunkSize);

                foreach ($matchChunks as $chunkIndex => $matchChunk) {
                    $scheduleMatchData = [];

                    foreach ($matchChunk as $index => $match) {
                        $globalIndex = ($chunkIndex * $chunkSize) + $index;

                        if (!isset($timeSlots[$globalIndex])) {
                            Log::error("Time slot not found for match index {$globalIndex}");
                            continue;
                        }

                        $scheduleMatchData[] = [
                            'tournament_id' => $tournamentId,
                            'stage_id' => $stageId,
                            'match_id' => $match['id'],
                            'time_slot_id' => $timeSlots[$globalIndex]['id'],
                            'referee_ids' => json_encode([]),
                            'created_at' => now(),
                            'updated_at' => now(),
                        ];

                        $currentEndTime = Carbon::parse($timeSlots[$globalIndex]['end_time'], 'UTC');
                        if ($currentEndTime && (!$lastDate || $currentEndTime > Carbon::parse($lastDate, 'UTC'))) {
                            $lastDate = $currentEndTime->format('Y-m-d');
                        }
                    }

                    // Optimize: Batch insert/update schedule matches
                    if (!empty($scheduleMatchData)) {
                        $this->batchCreateOrUpdateScheduleMatches($scheduleMatchData);
                    }
                }

                Log::info("Completed processing {$matchCount} matches for location {$locationId}");
            }

            // Performance logging
            $executionTime = microtime(true) - $methodStartTime;
            $memoryUsage = memory_get_peak_usage(true) / 1024 / 1024; // MB

            Log::info("planLeagueAndGroup completed", [
                'stage_id' => $stageId,
                'total_matches' => $totalMatches,
                'execution_time' => round($executionTime, 2) . 's',
                'memory_usage' => round($memoryUsage, 2) . 'MB',
                'chunk_size' => $chunkSize
            ]);

            return $lastDate;

        } catch (Exception $error) {
            $executionTime = microtime(true) - $methodStartTime;
            Log::error('Error in planLeagueAndGroup', [
                'stage_id' => $stageId,
                'error' => $error->getMessage(),
                'execution_time' => round($executionTime, 2) . 's',
                'trace' => $error->getTraceAsString()
            ]);
            throw new Error($error->getMessage());
        }
    }

    /**
     * Optimized batch create/update for schedule matches
     * Reduces database queries from N to 2 (select + upsert)
     */
    public function batchCreateOrUpdateScheduleMatches(array $scheduleMatchData)
    {
        if (empty($scheduleMatchData)) {
            return;
        }

        $matchIds = array_column($scheduleMatchData, 'match_id');
        $tournamentId = $scheduleMatchData[0]['tournament_id'];
        $stageId = $scheduleMatchData[0]['stage_id'];

        // Get existing matches in one query
        $existingMatches = ScheduleMatch::where('tournament_id', $tournamentId)
            ->where('stage_id', $stageId)
            ->whereIn('match_id', $matchIds)
            ->pluck('match_id')
            ->toArray();

        $toUpdate = [];
        $toInsert = [];

        foreach ($scheduleMatchData as $data) {
            if (in_array($data['match_id'], $existingMatches)) {
                $toUpdate[] = $data;
            } else {
                $toInsert[] = $data;
            }
        }

        // Batch insert new records
        if (!empty($toInsert)) {
            ScheduleMatch::insert($toInsert);
        }

        // Batch update existing records
        if (!empty($toUpdate)) {
            foreach ($toUpdate as $updateData) {
                ScheduleMatch::where('tournament_id', $updateData['tournament_id'])
                    ->where('stage_id', $updateData['stage_id'])
                    ->where('match_id', $updateData['match_id'])
                    ->update([
                        'time_slot_id' => $updateData['time_slot_id'],
                        'updated_at' => $updateData['updated_at'],
                    ]);
            }
        }
    }

    public function createOrUpdateScheduleMatch(
        int $tournamentId,
        int $stageId,
        int $matchId,
        int $timeSlotId
    ) {
        // Check if the match already exists
        $existingMatch = ScheduleMatch::where('tournament_id', $tournamentId)
            ->where('stage_id', $stageId)
            ->where('match_id', $matchId)
            ->first();

        if ($existingMatch) {
            $existingMatch->update([
                'time_slot_id' => $timeSlotId,
            ]);
        } else {
            ScheduleMatch::create([
                'tournament_id' => $tournamentId,
                'stage_id' => $stageId,
                'match_id' => $matchId,
                'time_slot_id' => $timeSlotId,
                'referee_ids' => [], // init referee_ids before assign
            ]);
        }
    }

    public function divideMatchesByLocation($listMatches, $listLocationIds, $type = 'Groups')
    {
        $locationMatches = [];
        $numsOfLocation = count($listLocationIds);

        // Initialize empty arrays for each location
        foreach ($listLocationIds as $locationId) {
            $locationMatches[$locationId] = [];
        }

        if ($type == config('constants.tournament_types.groups')) {
            $groupedMatches = [];

            // Group matches by 'group' or 'section'
            foreach ($listMatches as $match) {
                $groupKey = $match['group_name'] ?? 'default';
                $groupedMatches[$groupKey][] = $match;
            }

            $groupIndex = 0;
            foreach ($groupedMatches as $groupMatches) {
                $locationIndex = $groupIndex % $numsOfLocation;
                $locationId = $listLocationIds[$locationIndex];

                // Merge matches
                $locationMatches[$locationId] = array_merge(
                    $locationMatches[$locationId] ?? [],
                    $groupMatches
                );

                $groupIndex++;
            }
        } else {
            // Distribute matches evenly across locations
            foreach ($listMatches as $index => $match) {
                $locationIndex = $index % $numsOfLocation;
                $locationId = $listLocationIds[$locationIndex];

                // Merge matches
                $locationMatches[$locationId][] = $match;
            }
        }

        return [
            'locationMatches' => $locationMatches,
            'maxMatchesPerLocation' => max(array_map('count', $locationMatches)),
        ];
    }

    public function planKnockout(
        int $tournamentId,
        int $groupStageId,
        array $listKnockoutStages,
        array $listLocations,
        int $matchDuration,
        int $breakMatchDuration,
        string|null $forceBeginDate = null,
        string|null $forceBeginTime = null,
        string|null $endTimeOfDay = null,
        string|null $timeZone = null,
    ) {
        try {
            // Default timezone to UTC if not provided
            $timeZone = $timeZone ?? 'UTC';

            if (!$forceBeginDate && !$forceBeginTime) {
                $lastMatchTime = $this->getLastMatchTimeOfStage($tournamentId, $groupStageId);

                if (!$lastMatchTime) {
                    throw new Exception('No matches found for the group stage');
                }
                $modifyTime = $lastMatchTime->addMinutes($breakMatchDuration);

                // Convert to the target timezone for begin date/time
                $modifyTimeInTimezone = $modifyTime->setTimezone($timeZone);
                $beginDate = $modifyTimeInTimezone->format('Y-m-d');
                $beginTime = $modifyTimeInTimezone->format('H:i:s');
            } else {
                $beginDate = $forceBeginDate;
                $beginTime = $forceBeginTime;
            }

            $locationCount = count($listLocations);

            if ($locationCount === 0) {
                return [];
            }

            // Init list by location
            $locationTime = []; // locationId => list of time ranges

            foreach ($listLocations as $locationId) {
                $locationTime[$locationId] = [
                    'date' => $beginDate,
                    'time' => $beginTime,
                ];
            }

            foreach ($listKnockoutStages as $stageIndex => $stage) {
                // Get cancel match types from constants to filter out cancelled matches
                $cancelMatchTypes = config('constants.cancel_match_types');

                // Optimize: Select only necessary fields and order for consistent processing
                $stageMatches = StageMatch::select(['id', 'stage_id', 'status', 'order'])
                    ->where('stage_id', $stage)
                    ->where(function ($query) use ($cancelMatchTypes) {
                        $query->whereNotIn('status', $cancelMatchTypes)
                            ->orWhereNull('status');
                    })
                    ->orderBy('order')
                    ->orderBy('id')
                    ->get();

                if ($stageMatches->isEmpty()) {
                    Log::info("No valid matches found for knockout stage {$stage}");
                    continue;
                }

                $listMatchIds = $stageMatches->pluck('id')->toArray();
                $numsMatches = $stageMatches->count();

                Log::info("Processing {$numsMatches} knockout matches for stage {$stage}");

                $this->timeSlotController->deleteAllTimeSlotInTournament($tournamentId, $listMatchIds);

                $locationIndex = $stageIndex % $locationCount;
                $locationId = $listLocations[$locationIndex];

                $this->scheduleConfigController->updateOrCreateConfig(
                    $tournamentId,
                    $locationId,
                    $beginDate,
                    $beginTime,
                    $endTimeOfDay,
                    $matchDuration,
                    $breakMatchDuration,
                    $timeZone,
                );

                $currentLocationDate = $locationTime[$locationId]['date'];
                $currentLocationTime = $locationTime[$locationId]['time'];

                $timeSlots = $this->timeSlotController->generateTimeSlots(
                    $tournamentId,
                    $locationId,
                    $stage,
                    $currentLocationDate,
                    $currentLocationTime,
                    null,
                    $matchDuration,
                    $breakMatchDuration,
                    $numsMatches,
                    $timeZone,
                    false
                );

                if (count($timeSlots) !== $numsMatches) {
                    throw new Exception("Time slot count mismatch for stage {$stage}: expected {$numsMatches}, got " . count($timeSlots));
                }

                // Optimize: Batch process knockout matches
                $scheduleMatchData = [];
                foreach ($stageMatches as $matchIndex => $match) {
                    $scheduleMatchData[] = [
                        'tournament_id' => $tournamentId,
                        'stage_id' => $stage,
                        'match_id' => $match->id,
                        'time_slot_id' => $timeSlots[$matchIndex]['id'],
                        'referee_ids' => json_encode([]),
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }

                $this->batchCreateOrUpdateScheduleMatches($scheduleMatchData);

                // Parse the last match end time correctly (it's stored in UTC)
                $lastSlotEndTimeUTC = Carbon::parse($timeSlots[$numsMatches - 1]['end_time'], 'UTC');
                $parseLastMatchTime = $lastSlotEndTimeUTC->addMinutes($breakMatchDuration);

                // Convert to the target timezone for next stage calculation
                $parseLastMatchTimeInTimezone = $parseLastMatchTime->setTimezone($timeZone);
                $locationTime[$locationId]['date'] = $parseLastMatchTimeInTimezone->format('Y-m-d');
                $locationTime[$locationId]['time'] = $parseLastMatchTimeInTimezone->format('H:i:s');
            }

            return true;
        } catch (Exception $error) {
            Log::info('error in makePlanMatchesForKnockout', [$error]);
            throw new Error($error->getMessage());
        }
    }

    private function assignRefereesToMatches($tournamentId, $stageId, $numsOfReferees, $listRefereeIds)
    {
        try {
            DB::beginTransaction();

            // Validate input parameters
            if (empty($listRefereeIds)) {
                throw new Exception('No referees provided for assignment');
            }

            if ($numsOfReferees < 1) {
                return;
            }

            // Check if we have enough referees to assign at least one per match
            if (count($listRefereeIds) < 1) {
                throw new Exception("Insufficient referees: Need at least 1 referee but only " . count($listRefereeIds) . " provided");
            }

            // Get all scheduled matches for this tournament and stage
            $scheduledMatches = ScheduleMatch::with(['timeSlot'])
                ->where('tournament_id', $tournamentId)
                ->where('stage_id', $stageId)
                ->whereHas('timeSlot') // Only matches with time slots
                ->orderBy('time_slot_id')
                ->get();

            if ($scheduledMatches->isEmpty()) {
                Log::info("No scheduled matches found for tournament {$tournamentId}, stage {$stageId}");
                DB::commit();
                return true;
            }

            $totalMatches = $scheduledMatches->count();
            $refereeAssignments = $this->distributeReferees($scheduledMatches, $listRefereeIds, $numsOfReferees);

            foreach ($refereeAssignments as $matchId => $assignedRefereeIds) {
                ScheduleMatch::where('id', $matchId)->update([
                    'referee_ids' => $assignedRefereeIds,
                ]);
            }

            DB::commit();
            Log::info("Successfully assigned referees to {$totalMatches} matches");
            return true;
        } catch (Exception $error) {
            DB::rollBack();
            Log::error("Error assigning referees: " . $error->getMessage());
            throw $error;
        }
    }

    private function distributeReferees($scheduledMatches, $listRefereeIds, $numsOfReferees)
    {
        $assignments = [];
        $refereeWorkload = array_fill_keys($listRefereeIds, 0); // Referee ID => number of matches assigned
        $refereeSchedule = [];                                  // Track referee time conflicts: referee_id => [time_slots]

        // Sort matches by start time to handle them chronologically
        $sortedMatches = $scheduledMatches->sortBy(function ($match) {
            return $match->timeSlot->start_time ?? '9999-12-31 23:59:59';
        });

        foreach ($sortedMatches as $match) {
            $matchId = $match->id;
            $timeSlot = $match->timeSlot;

            if (!$timeSlot) {
                Log::warning("Match {$matchId} has no time slot, skipping referee assignment");
                continue;
            }

            $timeSlotStartTime = $timeSlot->start_time;
            $timeSlotEndTime = $timeSlot->end_time;

            // Find available referees for this time slot
            $availableReferees = $this->getAvailableReferees(
                $listRefereeIds,
                $refereeSchedule,
                $timeSlotStartTime,
                $timeSlotEndTime
            );

            // If no referees are available due to time conflicts, use least busy referees
            if (empty($availableReferees)) {
                Log::warning("No referees available for match {$matchId} due to time conflicts, using least busy referees");
                $availableReferees = $listRefereeIds;
            }

            // Sort available referees by workload (the least busy first)
            usort($availableReferees, function ($a, $b) use ($refereeWorkload) {
                return $refereeWorkload[$a] <=> $refereeWorkload[$b];
            });

            // Assign referees to this match
            $assignedReferees = [];
            $refereesToAssign = min($numsOfReferees, count($availableReferees));

            // Ensure at least one referee is assigned
            $refereesToAssign = max(1, $refereesToAssign);

            for ($i = 0; $i < $refereesToAssign && $i < count($availableReferees); $i++) {
                $refereeId = $availableReferees[$i];
                $assignedReferees[] = $refereeId;

                // Update referee workload
                $refereeWorkload[$refereeId]++;

                // Update referee schedule to track time conflicts
                if (!isset($refereeSchedule[$refereeId])) {
                    $refereeSchedule[$refereeId] = [];
                }
                $refereeSchedule[$refereeId][] = [
                    'start_time' => $timeSlotStartTime,
                    'end_time' => $timeSlotEndTime,
                    'match_id' => $matchId,
                ];
            }

            $assignments[$matchId] = $assignedReferees;
        }

        return $assignments;
    }

    private function getAvailableReferees($allRefereeIds, $refereeSchedule, $timeSlotStartTime, $timeSlotEndTime)
    {
        $availableReferees = [];

        foreach ($allRefereeIds as $refereeId) {
            if (!isset($refereeSchedule[$refereeId])) {
                // Referee has no scheduled matches yet
                $availableReferees[] = $refereeId;
                continue;
            }

            $isConflict = false;
            foreach ($refereeSchedule[$refereeId] as $refereeScheduled) {
                if ($this->checkTimeConflict($timeSlotStartTime, $timeSlotEndTime, $refereeScheduled['start_time'], $refereeScheduled['end_time'])) {
                    $isConflict = true;
                    break;
                }
            }

            if (!$isConflict) {
                $availableReferees[] = $refereeId;
            }
        }

        return $availableReferees;
    }

    private function checkTimeConflict($start1, $end1, $start2, $end2)
    {
        try {
            $start1 = new DateTime($start1);
            $end1 = new DateTime($end1);
            $start2 = new DateTime($start2);
            $end2 = new DateTime($end2);

            return $start1 < $end2 && $start2 < $end1;
        } catch (Exception $e) {
            Log::error("Error checking time conflict: " . $e->getMessage());
            return true;
        }
    }

    public function updateReferees(Request $request)
    {
        try {

            $request->validate([
                'time_slot_id' => "required|integer|exists:schedule_time_slots,id",
                'list_referees' => "required|array",
            ]);

            $scheduleMatch = ScheduleMatch::where('time_slot_id', $request->get('time_slot_id'))->first();

            $scheduleMatch->update([
                'referee_ids' => $request->get('list_referees'),
            ]);

            $stageMatchRefereesController = new StageMatchRefereeController();

            foreach ($request->get('list_referees') as $refereeId) {

                Log::info('$refereeId', [$refereeId]);
                // Check if referee exists in stage match referees
                $stageMatchRefereesController->assignRefereeToStageMatch($scheduleMatch->match_id, $refereeId);
            }



            return response()->json([
                "message" => "Update referees successfully",
            ]);
        } catch (Exception $error) {
            return response()->json([
                "message" => $error->getMessage(),
            ]);
        }
    }

    public function getScheduleConflict($tournamentId, $date, $timezone)
    {
        $scheduleMatches = ScheduleMatch::where('tournament_id', $tournamentId)
            ->with(['timeSlot', 'match.homeTeam', 'match.awayTeam'])
            ->whereNotNull('time_slot_id')
            ->get()->filter(function ($match) use ($date, $timezone) {
                $startTimeInLocal = Carbon::parse($match->timeSlot->start_time, 'UTC')->setTimezone($timezone);

                return $startTimeInLocal->format('Y-m-d') == $date;
            })->values();

        $conflicts = [
            'team_scheduling_conflict' => [],
            'referee_scheduling_conflict' => [],
            'stage_scheduling_conflict' => [],
        ];

        $allRefereeIds = collect();

        foreach ($scheduleMatches as $match) {
            if ($match->referee_ids && is_array($match->referee_ids)) {
                $allRefereeIds = $allRefereeIds->merge($match->referee_ids);
            }
        }
        $allRefereeIds = $allRefereeIds->unique()->filter()->values();

        // Fetch all referee
        $refereeDetails = [];
        if ($allRefereeIds->isNotEmpty()) {
            $referees = SeasonReferee::whereIn('id', $allRefereeIds)->with('user')->get();
            $refereeDetails = $referees->keyBy('id')->toArray();
        }

        $totalMatches = $scheduleMatches->count();

        // Iterate through each match to check conflicts
        for ($i = 0; $i < $totalMatches; $i++) {
            $currentMatch = $scheduleMatches[$i];

            // Skip if match doesn't have required data
            if (!$currentMatch->match || !$currentMatch->timeSlot) {
                continue;
            }

            $currentHomeTeamId = $currentMatch->match->home_team_id;
            $currentAwayTeamId = $currentMatch->match->away_team_id;
            $currentStartTime = $currentMatch->timeSlot->start_time;
            $currentEndTime = $currentMatch->timeSlot->end_time;

            // Check against all other matches
            for ($j = $i + 1; $j < $totalMatches; $j++) {
                $compareMatch = $scheduleMatches[$j];

                if (!$compareMatch->match || !$compareMatch->timeSlot) {
                    continue;
                }

                $compareHomeTeamId = $compareMatch->match->home_team_id;
                $compareAwayTeamId = $compareMatch->match->away_team_id;
                $compareStartTime = $compareMatch->timeSlot->start_time;
                $compareEndTime = $compareMatch->timeSlot->end_time;

                // check time overlap
                if ($this->checkTimeConflict($currentStartTime, $currentEndTime, $compareStartTime, $compareEndTime)) {
                    $conflictingTeams = [];

                    // Check if home team conflicts
                    if ($currentHomeTeamId != null && ($currentHomeTeamId == $compareHomeTeamId || $currentHomeTeamId == $compareAwayTeamId)) {
                        $conflictingTeams[] = [
                            'team_id' => $currentHomeTeamId,
                            'team_name' => $currentMatch->match->homeTeam->name ?? 'Unknown Team',
                        ];
                    }

                    // Check if away team conflicts
                    if ($currentAwayTeamId != null && ($currentAwayTeamId == $compareHomeTeamId || $currentAwayTeamId == $compareAwayTeamId)) {
                        $conflictingTeams[] = [
                            'team_id' => $currentAwayTeamId,
                            'team_name' => $currentMatch->match->awayTeam->name ?? 'Unknown Team',
                        ];
                    }

                    if (!empty($conflictingTeams)) {
                        $conflicts['team_scheduling_conflict'][$currentMatch->id] = $conflictingTeams;
                        $conflicts['team_scheduling_conflict'][$compareMatch->id] = $conflictingTeams;
                    }

                    // Check for referee conflicts
                    $currentRefereeIds = $currentMatch->referee_ids ?? [];
                    $compareRefereeIds = $compareMatch->referee_ids ?? [];

                    if (!empty($currentRefereeIds) && !empty($compareRefereeIds)) {

                        // Find same referee IDs between the two matches
                        $conflictingRefereeIds = array_intersect($currentRefereeIds, $compareRefereeIds);

                        if (!empty($conflictingRefereeIds)) {
                            $conflictingReferees = [];

                            foreach ($conflictingRefereeIds as $refereeId) {
                                $refereeInfo = $refereeDetails[$refereeId] ?? null;
                                $refereeName = 'Unknown Referee';

                                if ($refereeInfo) {
                                    if ($refereeInfo['referee_type'] === 'user' && isset($refereeInfo['user'])) {
                                        $refereeName = trim(($refereeInfo['user']['first_name'] ?? '') . ' ' . ($refereeInfo['user']['last_name'] ?? ''));
                                    } else {
                                        $refereeName = $refereeInfo['referee_name'] ?? 'Unknown Referee';
                                    }
                                }

                                $conflictingReferees[] = [
                                    'referee_id' => $refereeId,
                                    'referee_name' => $refereeName,
                                ];
                            }

                            $conflicts['referee_scheduling_conflict'][$currentMatch->id] = $conflictingReferees;
                            $conflicts['referee_scheduling_conflict'][$compareMatch->id] = $conflictingReferees;
                        }
                    }
                }
            }
        }

        $lastMatchOfGroupStage = ScheduleTimeSlot::where('tournament_id', $tournamentId)
            ->whereHas('stage', function ($query) {
                $query->where('type', config('constants.tournament_types.groups'));
            })
            ->orderBy('end_time', 'desc')
            ->first();

        $listKnockoutStages = Stage::where('tournament_id', $tournamentId)
            ->where('type', config('constants.tournament_types.knockout'))
            ->pluck('id')
            ->toArray();

        foreach ($listKnockoutStages as $stageId) {
            $firstMatchOfStage = ScheduleTimeSlot::where('tournament_id', $tournamentId)
                ->where('stage_id', $stageId)
                ->orderBy('start_time', 'asc')
                ->first();

            if ($firstMatchOfStage && $lastMatchOfGroupStage) {
                if ($firstMatchOfStage->start_time < $lastMatchOfGroupStage->end_time) {
                    $conflicts['stage_scheduling_conflict'][$stageId] = [
                        'message' => 'Knockout stage starts before group stage ends',
                    ];
                }
            }
        }

        return $conflicts;
    }

    public function submitSchedule($tournament_id)
    {
        try {
            request()->headers->set('X-Time-Zone', 'UTC');

            $scheduleMatches = ScheduleMatch::where('tournament_id', $tournament_id)
                ->with(['timeSlot', 'match.homeTeam', 'match.awayTeam'])
                ->whereNotNull('time_slot_id')
                ->get();

            foreach ($scheduleMatches as $scheduleMatch) {

                $matchRefereeController = new StageMatchRefereeController();
                $matchRefereeController->clearAllRefereeInMatch($scheduleMatch->match_id);
                foreach ($scheduleMatch->referee_ids as $referee_id) {
                    $matchRefereeController->assignRefereeToStageMatch($scheduleMatch->match_id, $referee_id);
                }
                $timeSlotStartUTC = $scheduleMatch->timeSlot->getOriginal('start_time');
                $timeSlotEndUTC = $scheduleMatch->timeSlot->getOriginal('end_time');

                $scheduleMatch->match->start_time = $timeSlotStartUTC;
                $scheduleMatch->match->end_time = $timeSlotEndUTC;
                $scheduleMatch->match->location_id = $scheduleMatch->timeSlot->location_id;
                $scheduleMatch->match->save();
            }

            return response()->json([
                "message" => "Auto submit schedule successfully",
            ]);
        } catch (Exception $error) {
            return response()->json([
                "message" => $error->getMessage(),
            ]);
        }
    }
}
