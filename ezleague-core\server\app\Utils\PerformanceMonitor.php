<?php

namespace App\Utils;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class PerformanceMonitor
{
    private static array $timers = [];
    private static array $queryCounters = [];
    private static array $memorySnapshots = [];

    /**
     * Start timing a specific operation
     */
    public static function startTimer(string $operation): void
    {
        self::$timers[$operation] = [
            'start' => microtime(true),
            'memory_start' => memory_get_usage(true),
            'queries_start' => self::getQueryCount()
        ];
    }

    /**
     * End timing and log performance metrics
     */
    public static function endTimer(string $operation, array $context = []): array
    {
        if (!isset(self::$timers[$operation])) {
            Log::warning("Timer not found for operation: {$operation}");
            return [];
        }

        $timer = self::$timers[$operation];
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        $endQueries = self::getQueryCount();

        $metrics = [
            'operation' => $operation,
            'execution_time' => round($endTime - $timer['start'], 4),
            'memory_used' => round(($endMemory - $timer['memory_start']) / 1024 / 1024, 2), // MB
            'peak_memory' => round(memory_get_peak_usage(true) / 1024 / 1024, 2), // MB
            'queries_executed' => $endQueries - $timer['queries_start'],
            'context' => $context
        ];

        // Log performance metrics
        self::logPerformanceMetrics($metrics);

        // Clean up
        unset(self::$timers[$operation]);

        return $metrics;
    }

    /**
     * Take a memory snapshot
     */
    public static function snapshot(string $label): void
    {
        self::$memorySnapshots[$label] = [
            'timestamp' => microtime(true),
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true)
        ];
    }

    /**
     * Get memory usage between snapshots
     */
    public static function getMemoryDiff(string $startLabel, string $endLabel): array
    {
        if (!isset(self::$memorySnapshots[$startLabel]) || !isset(self::$memorySnapshots[$endLabel])) {
            return [];
        }

        $start = self::$memorySnapshots[$startLabel];
        $end = self::$memorySnapshots[$endLabel];

        return [
            'time_diff' => round($end['timestamp'] - $start['timestamp'], 4),
            'memory_diff' => round(($end['memory_usage'] - $start['memory_usage']) / 1024 / 1024, 2), // MB
            'peak_memory' => round($end['memory_peak'] / 1024 / 1024, 2) // MB
        ];
    }

    /**
     * Monitor database query performance
     */
    public static function monitorQueries(callable $callback, string $operation = 'database_operation')
    {
        $startQueries = self::getQueryCount();
        $startTime = microtime(true);

        // Enable query logging temporarily
        DB::enableQueryLog();

        try {
            $result = $callback();
            
            $endTime = microtime(true);
            $queries = DB::getQueryLog();
            $queryCount = count($queries);

            // Log slow queries
            foreach ($queries as $query) {
                if ($query['time'] > 100) { // Queries taking more than 100ms
                    Log::warning("Slow query detected", [
                        'operation' => $operation,
                        'query' => $query['query'],
                        'bindings' => $query['bindings'],
                        'time' => $query['time'] . 'ms'
                    ]);
                }
            }

            Log::info("Database operation completed", [
                'operation' => $operation,
                'query_count' => $queryCount,
                'total_time' => round(($endTime - $startTime) * 1000, 2) . 'ms',
                'avg_query_time' => $queryCount > 0 ? round(array_sum(array_column($queries, 'time')) / $queryCount, 2) . 'ms' : '0ms'
            ]);

            return $result;
        } finally {
            DB::disableQueryLog();
        }
    }

    /**
     * Log performance metrics with appropriate level
     */
    private static function logPerformanceMetrics(array $metrics): void
    {
        $level = 'info';
        
        // Determine log level based on performance thresholds
        if ($metrics['execution_time'] > 30) { // 30+ seconds
            $level = 'error';
        } elseif ($metrics['execution_time'] > 10) { // 10+ seconds
            $level = 'warning';
        } elseif ($metrics['memory_used'] > 256) { // 256+ MB
            $level = 'warning';
        } elseif ($metrics['queries_executed'] > 100) { // 100+ queries
            $level = 'warning';
        }

        Log::log($level, "Performance metrics for {$metrics['operation']}", $metrics);

        // Additional alerting for critical performance issues
        if ($level === 'error') {
            self::alertCriticalPerformance($metrics);
        }
    }

    /**
     * Alert for critical performance issues
     */
    private static function alertCriticalPerformance(array $metrics): void
    {
        // This could be extended to:
        // - Send Slack notifications
        // - Create monitoring alerts
        // - Update performance dashboards
        // - Trigger auto-scaling
        
        Log::critical("Critical performance issue detected", [
            'operation' => $metrics['operation'],
            'execution_time' => $metrics['execution_time'] . 's',
            'memory_used' => $metrics['memory_used'] . 'MB',
            'queries' => $metrics['queries_executed'],
            'context' => $metrics['context']
        ]);
    }

    /**
     * Get current query count (approximation)
     */
    private static function getQueryCount(): int
    {
        // This is a simple approximation - in production you might want to use
        // a more sophisticated query counting mechanism
        return count(DB::getQueryLog());
    }

    /**
     * Generate performance report
     */
    public static function generateReport(array $operations = []): array
    {
        $report = [
            'timestamp' => now()->toISOString(),
            'memory_usage' => [
                'current' => round(memory_get_usage(true) / 1024 / 1024, 2) . 'MB',
                'peak' => round(memory_get_peak_usage(true) / 1024 / 1024, 2) . 'MB'
            ],
            'active_timers' => array_keys(self::$timers),
            'snapshots' => count(self::$memorySnapshots)
        ];

        if (!empty($operations)) {
            $report['operations'] = $operations;
        }

        return $report;
    }

    /**
     * Clear all monitoring data
     */
    public static function clear(): void
    {
        self::$timers = [];
        self::$queryCounters = [];
        self::$memorySnapshots = [];
    }

    /**
     * Monitor a callable with automatic timing
     */
    public static function monitor(callable $callback, string $operation, array $context = [])
    {
        self::startTimer($operation);
        
        try {
            $result = $callback();
            self::endTimer($operation, $context);
            return $result;
        } catch (\Exception $e) {
            self::endTimer($operation, array_merge($context, ['error' => $e->getMessage()]));
            throw $e;
        }
    }

    /**
     * Check if system is under high load
     */
    public static function isHighLoad(): bool
    {
        $memoryUsage = memory_get_usage(true) / 1024 / 1024; // MB
        $peakMemory = memory_get_peak_usage(true) / 1024 / 1024; // MB
        
        // Consider high load if using more than 512MB or peak is over 1GB
        return $memoryUsage > 512 || $peakMemory > 1024;
    }

    /**
     * Get system resource usage
     */
    public static function getResourceUsage(): array
    {
        return [
            'memory_current' => round(memory_get_usage(true) / 1024 / 1024, 2),
            'memory_peak' => round(memory_get_peak_usage(true) / 1024 / 1024, 2),
            'memory_limit' => ini_get('memory_limit'),
            'execution_time_limit' => ini_get('max_execution_time'),
            'active_timers' => count(self::$timers)
        ];
    }
}
