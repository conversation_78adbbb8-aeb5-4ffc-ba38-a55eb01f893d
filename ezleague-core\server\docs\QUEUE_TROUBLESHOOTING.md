# Queue System Troubleshooting Guide

## 🚨 **Quick Diagnosis Steps**

### **Step 1: Check Queue Configuration**
```bash
php artisan queue:debug
```

This command will show:
- Queue configuration status
- Jobs table existence
- Current jobs in queue
- Failed jobs
- Worker commands

### **Step 2: Test Job Dispatch**
```bash
php artisan schedule:test-job YOUR_TOURNAMENT_ID
```

This will:
- Dispatch a test job
- Verify it appears in the queue
- Show queue status
- Provide worker commands

### **Step 3: Check Database Tables**
```sql
-- Check if jobs table exists and has data
SELECT COUNT(*) FROM jobs;

-- Check recent jobs
SELECT id, queue, attempts, created_at, available_at 
FROM jobs 
ORDER BY created_at DESC 
LIMIT 10;

-- Check failed jobs
SELECT COUNT(*) FROM failed_jobs;
```

## 🔧 **Common Issues and Fixes**

### **Issue 1: Jobs Not Appearing in Queue**

**Symptoms:**
- Async endpoint returns success
- No jobs in `jobs` table
- No error messages

**Diagnosis:**
```bash
php artisan queue:debug
php artisan schedule:test-job 1
```

**Fixes:**
1. **Check queue configuration:**
   ```bash
   # Verify .env settings
   grep QUEUE .env
   
   # Should show:
   QUEUE_CONNECTION=database
   ```

2. **Clear config cache:**
   ```bash
   php artisan config:clear
   php artisan cache:clear
   ```

3. **Verify jobs table:**
   ```bash
   php artisan migrate --path=database/migrations/2023_04_10_101234_create_jobs_table.php
   ```

### **Issue 2: Jobs in Queue But Not Processing**

**Symptoms:**
- Jobs visible in `jobs` table
- `php artisan queue:work` shows no activity
- Jobs remain unprocessed

**Diagnosis:**
```bash
# Check jobs in specific queues
php artisan queue:debug

# Try processing one job
php artisan queue:work --once
```

**Fixes:**
1. **Use correct queue names:**
   ```bash
   # For scheduling jobs
   php artisan queue:work --queue=light-scheduling,medium-scheduling,heavy-scheduling,default
   
   # Or specific queue
   php artisan queue:work --queue=light-scheduling --timeout=300
   ```

2. **Check for reserved jobs:**
   ```sql
   SELECT * FROM jobs WHERE reserved_at IS NOT NULL;
   ```
   
   If jobs are stuck as reserved:
   ```bash
   php artisan queue:restart
   ```

### **Issue 3: Jobs Failing Silently**

**Symptoms:**
- Jobs disappear from queue
- No success logs
- No failed jobs

**Diagnosis:**
```bash
# Check failed jobs
SELECT * FROM failed_jobs ORDER BY failed_at DESC LIMIT 5;

# Check Laravel logs
tail -f storage/logs/laravel.log
```

**Fixes:**
1. **Enable detailed logging:**
   ```bash
   # In .env
   LOG_LEVEL=debug
   ```

2. **Run worker with verbose output:**
   ```bash
   php artisan queue:work --verbose --timeout=1800
   ```

3. **Check for memory/timeout issues:**
   ```bash
   php artisan queue:work --memory=512 --timeout=1800
   ```

## 🛠 **Configuration Fixes**

### **1. Update Queue Configuration**

The queue configuration has been updated to include custom scheduling queues. Verify your `config/queue.php` includes:

```php
'light-scheduling' => [
    'driver' => 'database',
    'table' => 'jobs',
    'queue' => 'light-scheduling',
    'retry_after' => 300,
    'after_commit' => false,
],
// ... medium-scheduling and heavy-scheduling
```

### **2. Environment Configuration**

Ensure your `.env` file has:
```env
QUEUE_CONNECTION=database
```

### **3. Database Migration**

Ensure jobs table exists:
```bash
php artisan migrate
```

## 🚀 **Worker Management**

### **Production Worker Setup**

1. **Supervisor Configuration** (`/etc/supervisor/conf.d/laravel-worker.conf`):
```ini
[program:laravel-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /path/to/your/project/artisan queue:work --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=4
redirect_stderr=true
stdout_logfile=/path/to/your/project/storage/logs/worker.log
stopwaitsecs=3600
```

2. **Start Supervisor:**
```bash
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start laravel-worker:*
```

### **Development Worker Commands**

```bash
# Process all queues
php artisan queue:work --timeout=1800

# Process specific scheduling queues
php artisan queue:work --queue=light-scheduling,medium-scheduling,heavy-scheduling --timeout=1800

# Process with memory limit
php artisan queue:work --memory=512 --timeout=1800

# Process one job and exit (for testing)
php artisan queue:work --once
```

## 📊 **Monitoring Commands**

### **Queue Status**
```bash
# Custom debug command
php artisan queue:debug

# Laravel built-in
php artisan queue:monitor

# Check specific queue
php artisan queue:work --queue=light-scheduling --once --verbose
```

### **Clear Stuck Jobs**
```bash
# Clear all jobs
php artisan queue:debug --clear

# Restart workers
php artisan queue:restart

# Clear failed jobs
php artisan queue:flush
```

## 🔍 **Debugging Workflow**

### **1. Verify Job Dispatch**
```bash
# Test job creation
php artisan schedule:test-job YOUR_TOURNAMENT_ID

# Check if job appears in database
php artisan queue:debug
```

### **2. Test Job Processing**
```bash
# Process one job manually
php artisan queue:work --once --verbose

# Check logs
tail -f storage/logs/laravel.log | grep -E "(schedule|job|queue)"
```

### **3. Monitor Performance**
```bash
# Watch queue in real-time
watch -n 2 'php artisan queue:debug'

# Monitor worker output
php artisan queue:work --verbose
```

## 🚨 **Emergency Fixes**

### **If Nothing Works:**

1. **Reset Queue System:**
   ```bash
   php artisan queue:debug --clear
   php artisan config:clear
   php artisan cache:clear
   php artisan queue:restart
   ```

2. **Verify Database:**
   ```sql
   SHOW TABLES LIKE '%jobs%';
   DESCRIBE jobs;
   ```

3. **Test with Sync Queue:**
   ```bash
   # Temporarily in .env
   QUEUE_CONNECTION=sync
   
   # Test the endpoint
   # Then switch back to database
   QUEUE_CONNECTION=database
   ```

4. **Manual Job Processing:**
   ```bash
   # Force process all jobs
   php artisan queue:work --queue=light-scheduling,medium-scheduling,heavy-scheduling,default --timeout=1800 --memory=1024
   ```

## 📞 **Support Information**

### **Log Files to Check:**
- `storage/logs/laravel.log` - Application logs
- `storage/logs/worker.log` - Worker logs (if using Supervisor)

### **Database Tables:**
- `jobs` - Queued jobs
- `failed_jobs` - Failed jobs
- `migrations` - Verify migrations ran

### **Key Commands:**
```bash
# Comprehensive diagnosis
php artisan queue:debug

# Test job dispatch
php artisan schedule:test-job TOURNAMENT_ID

# Process jobs
php artisan queue:work --queue=light-scheduling,medium-scheduling,heavy-scheduling,default --timeout=1800
```

The queue system should now work correctly with proper job dispatch, processing, and monitoring capabilities.
