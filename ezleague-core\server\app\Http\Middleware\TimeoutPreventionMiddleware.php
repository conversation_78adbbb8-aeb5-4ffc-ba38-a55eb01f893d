<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TimeoutPreventionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Only apply to schedule generation endpoints
        if (!$this->isScheduleEndpoint($request)) {
            return $next($request);
        }

        // Set aggressive timeout prevention for schedule operations
        ini_set('max_execution_time', 600); // 10 minutes
        ini_set('memory_limit', '1024M'); // 1GB
        set_time_limit(600);

        // Log the request for monitoring
        Log::info('Schedule request started with timeout prevention', [
            'url' => $request->url(),
            'method' => $request->method(),
            'tournament_id' => $request->get('tournament_id'),
            'user_agent' => $request->userAgent(),
            'ip' => $request->ip()
        ]);

        $startTime = microtime(true);

        try {
            $response = $next($request);

            $executionTime = microtime(true) - $startTime;
            
            // Log successful completion
            Log::info('Schedule request completed successfully', [
                'execution_time' => round($executionTime, 2) . 's',
                'tournament_id' => $request->get('tournament_id'),
                'status_code' => $response->getStatusCode()
            ]);

            return $response;

        } catch (\Exception $e) {
            $executionTime = microtime(true) - $startTime;
            
            // Check if it's a timeout-related error
            if ($this->isTimeoutError($e)) {
                Log::error('Timeout detected in schedule request', [
                    'execution_time' => round($executionTime, 2) . 's',
                    'tournament_id' => $request->get('tournament_id'),
                    'error' => $e->getMessage(),
                    'suggestion' => 'Use async endpoint: /api/auto-schedule/generate-async'
                ]);

                return response()->json([
                    'message' => 'Request timed out. For large tournaments, please use the async endpoint.',
                    'async_endpoint' => '/api/auto-schedule/generate-async',
                    'execution_time' => round($executionTime, 2),
                    'error_type' => 'timeout'
                ], 408); // Request Timeout
            }

            // Re-throw non-timeout errors
            throw $e;
        }
    }

    /**
     * Check if this is a schedule-related endpoint
     */
    private function isScheduleEndpoint(Request $request): bool
    {
        $path = $request->path();
        
        return str_contains($path, 'auto-schedule/generate') || 
               str_contains($path, 'schedule-matches');
    }

    /**
     * Check if the exception is timeout-related
     */
    private function isTimeoutError(\Exception $e): bool
    {
        $message = strtolower($e->getMessage());
        
        $timeoutIndicators = [
            'timeout',
            'maximum execution time',
            'time limit',
            'connection timed out',
            'operation timed out',
            'aborted to prevent timeout'
        ];

        foreach ($timeoutIndicators as $indicator) {
            if (str_contains($message, $indicator)) {
                return true;
            }
        }

        return false;
    }
}
