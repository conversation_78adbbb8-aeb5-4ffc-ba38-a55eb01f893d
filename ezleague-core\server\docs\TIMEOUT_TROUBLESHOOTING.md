# Schedule Matches Timeout Troubleshooting Guide

## 🚨 **Immediate Solutions for 120-Second Timeout**

### **Quick Fix 1: Use the Diagnostic Endpoint**

First, check your tournament complexity:

```bash
POST /api/auto-schedule/diagnostic
{
    "tournament_id": YOUR_TOURNAMENT_ID,
    "stage_id": YOUR_STAGE_ID  // optional
}
```

This will tell you:
- How many matches need to be processed
- Recommended processing method (sync/async)
- Estimated execution time
- Suggested chunk size

### **Quick Fix 2: Use Async Processing**

For any tournament with more than 20 matches, use the async endpoint:

```bash
POST /api/auto-schedule/generate-async
{
    "tournament_id": YOUR_TOURNAMENT_ID,
    "list_location_ids": [1, 2, 3],
    "begin_date": "2025-07-01",
    "begin_time": "09:00",
    "match_duration": 90,
    "break_duration": 15
}
```

**Response:**
```json
{
    "message": "Schedule processing started in background",
    "job_id": "schedule_123_abc123",
    "estimated_matches": 150,
    "status": "processing"
}
```

### **Quick Fix 3: Force Sync with Small Chunks**

If you must use synchronous processing:

```bash
POST /api/auto-schedule/generate
{
    "tournament_id": YOUR_TOURNAMENT_ID,
    "list_location_ids": [1, 2, 3],
    "begin_date": "2025-07-01",
    "begin_time": "09:00",
    "match_duration": 90,
    "break_duration": 15,
    "chunk_size": 25,        // Small chunks
    "force_sync": true       // Override auto-async
}
```

## 🔍 **Root Cause Analysis**

### **1. Automatic Timeout Prevention**

The system now automatically:
- **Increases PHP execution time** to 10 minutes
- **Increases memory limit** to 1GB
- **Auto-switches to async** for tournaments with 30+ matches
- **Monitors execution time** and aborts before timeout
- **Uses smaller chunks** for large datasets

### **2. Timeout Sources**

The 120-second timeout could come from:

1. **Web Server (Apache/Nginx)**
   ```apache
   # Apache
   Timeout 300
   ProxyTimeout 300
   
   # Nginx
   proxy_read_timeout 300s;
   proxy_connect_timeout 300s;
   ```

2. **PHP Configuration**
   ```ini
   max_execution_time = 600
   memory_limit = 1024M
   max_input_time = 600
   ```

3. **Laravel/Database**
   ```php
   // Database timeout
   'mysql' => [
       'options' => [
           PDO::ATTR_TIMEOUT => 300,
       ],
   ],
   ```

## 📊 **Performance Thresholds**

| Match Count | Recommended Method | Expected Time | Chunk Size |
|-------------|-------------------|---------------|------------|
| 1-20        | Sync              | 5-30 seconds  | 100        |
| 21-50       | Sync (monitored)  | 30-90 seconds | 50         |
| 51-100      | Async             | 1-3 minutes   | 25         |
| 100+        | Async             | 2-10 minutes  | 25         |

## 🛠 **Configuration Updates**

### **1. Update PHP Configuration**

Create or update `php.ini`:
```ini
max_execution_time = 600
memory_limit = 1024M
max_input_time = 600
post_max_size = 100M
upload_max_filesize = 100M
```

### **2. Update Web Server**

**Apache (`httpd.conf` or `.htaccess`):**
```apache
Timeout 600
ProxyTimeout 600
LimitRequestBody 104857600
```

**Nginx:**
```nginx
proxy_read_timeout 600s;
proxy_connect_timeout 600s;
proxy_send_timeout 600s;
client_max_body_size 100M;
```

### **3. Update Laravel Configuration**

**Database timeout (`config/database.php`):**
```php
'mysql' => [
    'options' => [
        PDO::ATTR_TIMEOUT => 600,
        PDO::MYSQL_ATTR_READ_TIMEOUT => 600,
    ],
],
```

## 🚀 **Recommended Workflow**

### **Step 1: Diagnose**
```bash
POST /api/auto-schedule/diagnostic
{
    "tournament_id": 123
}
```

### **Step 2: Choose Method**
- **< 20 matches**: Use sync endpoint
- **20-50 matches**: Use sync with small chunks
- **> 50 matches**: Use async endpoint

### **Step 3: Monitor**
Check logs for:
```
Schedule matches completed
Performance metrics for schedule_matches_total
Chunk progress: X/Y for location Z
```

### **Step 4: Handle Errors**
If you get timeout errors:
1. Check the logs for specific error messages
2. Use the async endpoint
3. Reduce chunk size
4. Check system resources

## 📝 **Monitoring Commands**

### **Check Current Limits:**
```bash
php -i | grep -E "(max_execution_time|memory_limit)"
```

### **Monitor Queue Jobs:**
```bash
php artisan queue:work --queue=heavy-scheduling --timeout=1800
```

### **Check Logs:**
```bash
tail -f storage/logs/laravel.log | grep -E "(schedule|timeout|performance)"
```

## 🔧 **Emergency Fixes**

### **If Still Getting Timeouts:**

1. **Reduce chunk size to 10:**
   ```json
   {"chunk_size": 10}
   ```

2. **Process one stage at a time:**
   ```json
   {"stage_id": SPECIFIC_STAGE_ID}
   ```

3. **Use async for everything:**
   ```bash
   # Always use async endpoint
   POST /api/auto-schedule/generate-async
   ```

4. **Check system resources:**
   ```bash
   # Check memory usage
   free -h
   
   # Check CPU usage
   top
   
   # Check disk space
   df -h
   ```

## 📞 **Support Information**

### **Log Files to Check:**
- `storage/logs/laravel.log` - Application logs
- `/var/log/apache2/error.log` - Apache errors
- `/var/log/nginx/error.log` - Nginx errors

### **Key Metrics to Report:**
- Tournament ID
- Number of matches
- Execution time before timeout
- Error message
- Server specifications
- PHP version and configuration

### **Quick Health Check:**
```bash
POST /api/auto-schedule/diagnostic
{
    "tournament_id": YOUR_TOURNAMENT_ID
}
```

This will provide all the information needed to diagnose and resolve timeout issues.

## 🎯 **Success Indicators**

After implementing these fixes, you should see:
- ✅ No more 120-second timeouts
- ✅ Automatic async processing for large tournaments
- ✅ Progress logging in the application logs
- ✅ Successful completion within expected timeframes
- ✅ Proper error handling with helpful messages

The system is now designed to prevent timeouts proactively rather than reactively handle them.
