<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Queue;

class DebugQueueCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'queue:debug {--clear : Clear all jobs from queue}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Debug queue system and show current jobs';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== Queue System Debug ===');
        
        // Show current configuration
        $this->showConfiguration();
        
        // Show jobs in queue
        $this->showQueuedJobs();
        
        // Show failed jobs
        $this->showFailedJobs();
        
        // Clear jobs if requested
        if ($this->option('clear')) {
            $this->clearJobs();
        }
        
        // Show queue workers status
        $this->showWorkerStatus();
    }

    private function showConfiguration()
    {
        $this->info("\n--- Queue Configuration ---");
        $this->line("Default Connection: " . config('queue.default'));
        $this->line("Database Table: " . config('queue.connections.database.table'));
        
        // Check if jobs table exists
        $jobsTableExists = DB::getSchemaBuilder()->hasTable('jobs');
        $this->line("Jobs Table Exists: " . ($jobsTableExists ? 'YES' : 'NO'));
        
        if ($jobsTableExists) {
            $jobsCount = DB::table('jobs')->count();
            $this->line("Total Jobs in Queue: " . $jobsCount);
        }
        
        // Check failed jobs table
        $failedJobsTableExists = DB::getSchemaBuilder()->hasTable('failed_jobs');
        $this->line("Failed Jobs Table Exists: " . ($failedJobsTableExists ? 'YES' : 'NO'));
        
        if ($failedJobsTableExists) {
            $failedJobsCount = DB::table('failed_jobs')->count();
            $this->line("Total Failed Jobs: " . $failedJobsCount);
        }
    }

    private function showQueuedJobs()
    {
        $this->info("\n--- Queued Jobs ---");
        
        if (!DB::getSchemaBuilder()->hasTable('jobs')) {
            $this->error("Jobs table does not exist! Run: php artisan migrate");
            return;
        }
        
        $jobs = DB::table('jobs')
            ->select('id', 'queue', 'attempts', 'created_at', 'available_at', 'reserved_at')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();
            
        if ($jobs->isEmpty()) {
            $this->line("No jobs in queue");
            return;
        }
        
        $this->table(
            ['ID', 'Queue', 'Attempts', 'Created', 'Available', 'Reserved'],
            $jobs->map(function ($job) {
                return [
                    $job->id,
                    $job->queue,
                    $job->attempts,
                    date('Y-m-d H:i:s', $job->created_at),
                    date('Y-m-d H:i:s', $job->available_at),
                    $job->reserved_at ? date('Y-m-d H:i:s', $job->reserved_at) : 'Not Reserved'
                ];
            })->toArray()
        );
        
        // Show queue breakdown
        $queueBreakdown = DB::table('jobs')
            ->select('queue', DB::raw('count(*) as count'))
            ->groupBy('queue')
            ->get();
            
        $this->info("\n--- Queue Breakdown ---");
        foreach ($queueBreakdown as $queue) {
            $this->line("{$queue->queue}: {$queue->count} jobs");
        }
    }

    private function showFailedJobs()
    {
        $this->info("\n--- Failed Jobs ---");
        
        if (!DB::getSchemaBuilder()->hasTable('failed_jobs')) {
            $this->line("Failed jobs table does not exist");
            return;
        }
        
        $failedJobs = DB::table('failed_jobs')
            ->select('id', 'queue', 'failed_at')
            ->orderBy('failed_at', 'desc')
            ->limit(5)
            ->get();
            
        if ($failedJobs->isEmpty()) {
            $this->line("No failed jobs");
            return;
        }
        
        $this->table(
            ['ID', 'Queue', 'Failed At'],
            $failedJobs->map(function ($job) {
                return [
                    $job->id,
                    $job->queue,
                    $job->failed_at
                ];
            })->toArray()
        );
    }

    private function clearJobs()
    {
        $this->info("\n--- Clearing Jobs ---");
        
        $jobsCount = DB::table('jobs')->count();
        $failedJobsCount = DB::table('failed_jobs')->count();
        
        if ($jobsCount > 0) {
            DB::table('jobs')->delete();
            $this->line("Cleared {$jobsCount} queued jobs");
        }
        
        if ($failedJobsCount > 0) {
            DB::table('failed_jobs')->delete();
            $this->line("Cleared {$failedJobsCount} failed jobs");
        }
        
        if ($jobsCount === 0 && $failedJobsCount === 0) {
            $this->line("No jobs to clear");
        }
    }

    private function showWorkerStatus()
    {
        $this->info("\n--- Worker Commands ---");
        $this->line("To start queue workers, run these commands:");
        $this->line("");
        $this->line("# For all queues:");
        $this->line("php artisan queue:work --timeout=1800");
        $this->line("");
        $this->line("# For specific scheduling queues:");
        $this->line("php artisan queue:work --queue=light-scheduling --timeout=300");
        $this->line("php artisan queue:work --queue=medium-scheduling --timeout=900");
        $this->line("php artisan queue:work --queue=heavy-scheduling --timeout=1800");
        $this->line("");
        $this->line("# For default queue:");
        $this->line("php artisan queue:work --queue=default --timeout=300");
    }
}
